<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EX - 跨境云支付平台 | 革新全球支付，连接无界商机</title>
    <!-- 添加现代字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- 粒子效果库 -->
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    <style>
        :root {
            --primary-color: #2565f7;
            --primary-gradient: linear-gradient(135deg, #2565f7, #5d38eb);
            --secondary-color: #0b31a0;
            --dark-color: #051552;
            --accent-color: #00e5ff;
            --light-color: #e6f7ff;
            --text-color: #333333;
            --light-text: #ffffff;
            --bg-color: #f5f5f5;
            --section-padding: 80px 0;
            --card-bg: rgba(255, 255, 255, 0.95);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --box-shadow: 0 8px 32px 0 rgba(0, 0, 20, 0.1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            color: var(--text-color);
            background-color: #040d21;
            line-height: 1.6;
            overflow-x: hidden;
            background-image: radial-gradient(circle at 50% 50%, rgba(37, 101, 247, 0.1) 0%, rgba(5, 21, 82, 0.1) 100%);
        }
        
        /* 粒子背景 */
        #particles-js {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: -1;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
            z-index: 1;
        }
        
        /* 玻璃效果 */
        .glass {
            background: var(--glass-bg);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            box-shadow: var(--box-shadow);
        }
        
        /* 导航栏 */
        header {
            background-color: rgba(4, 13, 33, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
        }
        
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: var(--light-text);
            display: flex;
            align-items: center;
        }
        
        .logo::before {
            content: '';
            display: inline-block;
            width: 12px;
            height: 12px;
            background: var(--accent-color);
            border-radius: 50%;
            margin-right: 10px;
            box-shadow: 0 0 15px var(--accent-color);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(0, 229, 255, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(0, 229, 255, 0); }
            100% { box-shadow: 0 0 0 0 rgba(0, 229, 255, 0); }
        }
        
        .logo span {
            color: var(--accent-color);
            font-weight: 300;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
        }
        
        .nav-links li {
            margin-left: 35px;
            position: relative;
        }
        
        .nav-links a {
            text-decoration: none;
            color: var(--light-text);
            font-weight: 400;
            font-size: 15px;
            letter-spacing: 0.5px;
            transition: all 0.3s;
            padding: 8px 0;
            position: relative;
        }
        
        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--accent-color);
            transition: width 0.3s ease;
        }
        
        .nav-links a:hover {
            color: var(--accent-color);
        }
        
        .nav-links a:hover::after {
            width: 100%;
        }
        
        /* 主横幅 */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            color: var(--light-text);
            padding: 140px 0 80px;
            text-align: center;
            overflow: hidden;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--primary-gradient);
            opacity: 0.1;
            z-index: -1;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .hero h1 {
            font-size: 52px;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(to right, var(--light-text), var(--accent-color));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            position: relative;
            display: inline-block;
        }
        
        .hero h1::after {
            content: '';
            position: absolute;
            width: 60px;
            height: 4px;
            background: var(--accent-color);
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 2px;
        }
        
        .hero p {
            font-size: 20px;
            max-width: 800px;
            margin: 30px auto 50px;
            line-height: 1.8;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
            100% { transform: translateY(0px); }
        }
        
        .digital-circle {
            position: absolute;
            border-radius: 50%;
            border: 1px dashed rgba(255, 255, 255, 0.2);
            width: 300px;
            height: 300px;
            animation: rotate 20s linear infinite;
        }
        
        .digital-circle:nth-child(1) {
            top: 10%;
            left: 15%;
            width: 500px;
            height: 500px;
        }
        
        .digital-circle:nth-child(2) {
            bottom: 5%;
            right: 10%;
            width: 400px;
            height: 400px;
            animation-direction: reverse;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .btn {
            display: inline-block;
            background: rgba(0, 229, 255, 0.1);
            color: var(--light-text);
            padding: 14px 32px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 500;
            letter-spacing: 1px;
            border: 1px solid var(--accent-color);
            position: relative;
            z-index: 1;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--accent-color);
            z-index: -1;
            transition: all 0.4s;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 0 20px rgba(0, 229, 255, 0.4);
            color: #040d21;
        }
        
        .btn:hover::after {
            left: 0;
        }
        

        
        /* 特点部分 */
        .features {
            padding: var(--section-padding);
            background-color: #040d21;
            position: relative;
        }
        
        /* 全球网络部分 */
        .global-network {
            padding: var(--section-padding);
            background-color: #050e24;
            position: relative;
            overflow: hidden;
        }
        
        .global-network::before {
            content: '';
            position: absolute;
            width: 500px;
            height: 500px;
            background: radial-gradient(circle, rgba(0, 229, 255, 0.05) 0%, rgba(0, 229, 255, 0) 70%);
            top: -200px;
            right: -200px;
            border-radius: 50%;
            z-index: 0;
        }
        
        .global-network::after {
            content: '';
            position: absolute;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(37, 101, 247, 0.05) 0%, rgba(37, 101, 247, 0) 70%);
            bottom: -100px;
            left: -100px;
            border-radius: 50%;
            z-index: 0;
        }
        
        .world-map-container {
            display: grid;
            grid-template-columns: 3fr 2fr;
            gap: 40px;
            align-items: center;
            position: relative;
            z-index: 1;
        }
        
        .map-visual {
            position: relative;
        }
        
        .global-coverage-map {
            width: 100%;
            height: auto;
            max-height: 500px;
        }
        
        .region-node {
            animation: pulse 2s infinite;
        }
        
        .region-label {
            font-family: 'Poppins', sans-serif;
            opacity: 0.9;
        }
        
        .ex-hub {
            animation: hubPulse 3s infinite;
        }
        
        .connection-line {
            animation: flowLine 3s infinite linear;
        }
        
        @keyframes hubPulse {
            0% { r: 12; stroke-width: 2; }
            50% { r: 15; stroke-width: 3; }
            100% { r: 12; stroke-width: 2; }
        }
        
        @keyframes flowLine {
            from { stroke-dashoffset: 20; }
            to { stroke-dashoffset: 0; }
        }
        
        .coverage-stats {
            display: flex;
            justify-content: space-around;
            margin-top: 30px;
            padding: 15px;
            background: rgba(7, 21, 52, 0.5);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .coverage-item {
            text-align: center;
        }
        
        .coverage-number {
            display: block;
            font-size: 36px;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 5px;
        }
        
        .coverage-number sup {
            font-size: 18px;
            top: -0.8em;
            position: relative;
            vertical-align: baseline;
        }
        
        .coverage-label {
            display: block;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .map-description {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .map-description h3 {
            font-size: 24px;
            margin-bottom: 15px;
            color: var(--light-text);
            position: relative;
        }
        
        .map-description h3::after {
            content: '';
            position: absolute;
            width: 40px;
            height: 3px;
            background: var(--accent-color);
            bottom: -8px;
            left: 0;
        }
        
        .map-description p {
            line-height: 1.8;
            margin-bottom: 25px;
        }
        
        .compliance-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 25px;
        }
        
        .compliance-item {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 255, 255, 0.03);
            padding: 10px 15px;
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .compliance-item i {
            color: var(--accent-color);
            font-size: 18px;
        }
        
        .compliance-item span {
            font-size: 14px;
            color: var(--light-text);
        }
        
        /* 科技赋能部分 */
        .tech-empowerment {
            padding: var(--section-padding);
            background-color: #060f27;
            position: relative;
            overflow: hidden;
        }
        
        .tech-empowerment::before {
            content: '';
            position: absolute;
            width: 500px;
            height: 500px;
            background: radial-gradient(circle, rgba(37, 101, 247, 0.05) 0%, rgba(37, 101, 247, 0) 70%);
            top: -200px;
            left: -100px;
            border-radius: 50%;
            z-index: 0;
        }
        
        .tech-container {
            display: grid;
            grid-template-columns: 3fr 2fr;
            gap: 40px;
            align-items: center;
            position: relative;
            z-index: 1;
        }
        
        .tech-diagram {
            position: relative;
        }
        
        .microservice-diagram {
            width: 100%;
            height: auto;
        }
        
        .service-box {
            transition: all 0.3s;
        }
        
        .service-box:hover {
            fill: rgba(37, 101, 247, 0.3);
            stroke: #00e5ff;
        }
        
        .service-node {
            transition: all 0.3s;
        }
        
        .service-node:hover {
            r: 22;
            fill: rgba(0, 229, 255, 0.25);
        }
        
        .core-service {
            animation: pulse 3s infinite;
        }
        
        .service-line {
            transition: all 0.3s;
        }
        
        .infra-service {
            transition: all 0.3s;
        }
        
        .infra-service:hover {
            fill: rgba(37, 101, 247, 0.2);
            stroke: rgba(0, 229, 255, 0.7);
        }
        
        .tech-features {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .tech-features h3 {
            font-size: 24px;
            margin-bottom: 25px;
            color: var(--light-text);
            position: relative;
        }
        
        .tech-features h3::after {
            content: '';
            position: absolute;
            width: 40px;
            height: 3px;
            background: var(--accent-color);
            bottom: -8px;
            left: 0;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-list li {
            display: flex;
            margin-bottom: 25px;
            align-items: flex-start;
        }
        
        .feature-icon {
            background: rgba(37, 101, 247, 0.1);
            border: 1px solid rgba(37, 101, 247, 0.3);
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .feature-icon i {
            font-size: 22px;
            color: var(--accent-color);
        }
        
        .feature-content h4 {
            font-size: 18px;
            margin: 0 0 8px 0;
            color: var(--light-text);
        }
        
        .feature-content p {
            font-size: 14px;
            margin: 0;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.7);
        }
        
        /* 安全保障部分 */
        .security-assurance {
            padding: var(--section-padding);
            background-color: #040d21;
            position: relative;
            overflow: hidden;
        }
        
        .security-assurance::before {
            content: '';
            position: absolute;
            width: 400px;
            height: 400px;
            background: radial-gradient(circle, rgba(37, 101, 247, 0.05) 0%, rgba(37, 101, 247, 0) 70%);
            bottom: -200px;
            right: -100px;
            border-radius: 50%;
            z-index: 0;
        }
        
        .security-container {
            display: grid;
            grid-template-columns: 3fr 2fr;
            gap: 40px;
            align-items: center;
            position: relative;
            z-index: 1;
        }
        
        .security-layers {
            position: relative;
        }
        
        .security-diagram {
            width: 100%;
            height: auto;
        }
        
        .pulse-circle {
            animation: pulse 4s infinite;
        }
        
        .scan-line {
            animation: scanMove 3s infinite linear;
        }
        
        .security-point {
            transition: all 0.3s;
        }
        
        .security-point:hover {
            r: 28;
            fill: rgba(0, 229, 255, 0.2);
        }
        
        .data-point {
            animation: blink 2s infinite;
        }
        
        @keyframes scanMove {
            0% { transform: translateY(0); }
            50% { transform: translateY(150px); }
            100% { transform: translateY(0); }
        }
        
        @keyframes blink {
            0% { opacity: 0.3; }
            50% { opacity: 1; }
            100% { opacity: 0.3; }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
        }
        
        .security-features {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .security-features h3 {
            font-size: 24px;
            margin-bottom: 25px;
            color: var(--light-text);
            position: relative;
        }
        
        .security-features h3::after {
            content: '';
            position: absolute;
            width: 40px;
            height: 3px;
            background: var(--accent-color);
            bottom: -8px;
            left: 0;
        }
        
        .security-feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .security-feature-item {
            display: flex;
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s;
        }
        
        .security-feature-item:hover {
            background: rgba(37, 101, 247, 0.1);
            transform: translateY(-3px);
        }
        
        .security-icon {
            width: 40px;
            height: 40px;
            background: rgba(0, 229, 255, 0.1);
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .security-icon i {
            font-size: 18px;
            color: var(--accent-color);
        }
        
        .security-text h4 {
            font-size: 16px;
            margin: 0 0 5px 0;
            color: var(--light-text);
        }
        
        .security-text p {
            font-size: 13px;
            margin: 0;
            line-height: 1.5;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .features::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect x="0" y="0" width="100" height="100" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></svg>');
            opacity: 0.5;
            z-index: 0;
        }
        
        .section-title {
            text-align: center;
            margin-bottom: 70px;
            position: relative;
        }
        
        .section-title h2 {
            font-size: 36px;
            color: var(--light-text);
            margin-bottom: 15px;
            position: relative;
            display: inline-block;
        }
        
        .section-title h2::after {
            content: '';
            position: absolute;
            width: 50px;
            height: 3px;
            background: var(--accent-color);
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .section-title p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 18px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            position: relative;
            z-index: 1;
        }
        
        .feature-item {
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            background-color: rgba(11, 49, 160, 0.08);
            padding: 35px 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.06);
            transition: all 0.4s;
            position: relative;
            overflow: hidden;
        }
        
        .feature-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 0;
            background: var(--accent-color);
            transition: height 0.4s;
        }
        
        .feature-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 229, 255, 0.15);
            background-color: rgba(11, 49, 160, 0.15);
        }
        
        .feature-item:hover::before {
            height: 100%;
        }
        
        .feature-item h3 {
            font-size: 22px;
            margin-bottom: 20px;
            color: var(--light-text);
            display: flex;
            align-items: center;
        }
        
        .feature-item h3 i {
            margin-right: 15px;
            font-size: 24px;
            color: var(--accent-color);
        }
        
        .feature-item p {
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.7;
            font-size: 15px;
        }
        
        /* 架构图部分 */
        .architecture {
            padding: var(--section-padding);
            background-color: #050e24;
            position: relative;
            overflow: hidden;
        }
        
        .architecture::before, .architecture::after {
            content: '';
            position: absolute;
            background: radial-gradient(circle, rgba(0, 229, 255, 0.1) 0%, rgba(0, 229, 255, 0) 70%);
            width: 600px;
            height: 600px;
            border-radius: 50%;
            z-index: 0;
        }
        
        .architecture::before {
            top: -300px;
            left: -300px;
        }
        
        .architecture::after {
            bottom: -300px;
            right: -300px;
        }
        
        .architecture .section-title {
            color: var(--light-text);
        }
        
        .architecture-content {
            background-color: rgba(4, 13, 33, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 40px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.05);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow-x: auto;
            position: relative;
            z-index: 1;
        }
        
        .architecture-diagram {
            font-family: 'JetBrains Mono', 'Courier New', monospace;
            white-space: pre;
            font-size: 14px;
            line-height: 1.3;
            color: var(--light-text);
            text-shadow: 0 0 10px rgba(0, 229, 255, 0.3);
        }
        
        .architecture-diagram-wrapper {
            position: relative;
        }
        
        .tech-line {
            position: absolute;
            background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
            height: 1px;
            width: 100%;
            top: 50%;
            left: 0;
            opacity: 0.3;
            animation: scanLine 4s linear infinite;
        }
        
        @keyframes scanLine {
            0% {
                top: 0%;
            }
            100% {
                top: 100%;
            }
        }
        
        /* 客户案例 */
        .case-studies {
            padding: var(--section-padding);
            background-color: #fff;
        }
        
        .case-item {
            background-color: var(--light-color);
            padding: 40px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .case-item h3 {
            font-size: 24px;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }
        
        .case-item h4 {
            font-size: 18px;
            margin: 20px 0 10px;
            color: var(--dark-color);
        }
        
        .case-item ul {
            padding-left: 20px;
            margin: 15px 0;
        }
        
        .results {
            background-color: rgba(24, 144, 255, 0.1);
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        
        .results h4 {
            color: var(--primary-color);
        }
        
        /* 适用客户部分 */
        .clients {
            padding: var(--section-padding);
            background-color: var(--bg-color);
        }
        
        .client-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .client-card {
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            text-align: center;
        }
        
        .client-card h3 {
            font-size: 22px;
            margin: 20px 0;
            color: var(--secondary-color);
        }
        
        .client-icon {
            font-size: 48px;
            color: var(--primary-color);
        }
        
        /* 页脚 */
        footer {
            background-color: var(--dark-color);
            color: var(--light-text);
            padding: 60px 0 30px;
        }
        
        .footer-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
        }
        
        .footer-logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .footer-links h3 {
            font-size: 18px;
            margin-bottom: 20px;
        }
        
        .footer-links ul {
            list-style: none;
        }
        
        .footer-links li {
            margin-bottom: 10px;
        }
        
        .footer-links a {
            color: var(--light-text);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .footer-links a:hover {
            color: var(--primary-color);
        }
        
        .copyright {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        @media (max-width: 768px) {
            .navbar {
                flex-direction: column;
            }
            
            .nav-links {
                margin-top: 20px;
            }
            
            .nav-links li {
                margin: 0 10px;
            }
            
            .hero h1 {
                font-size: 36px;
            }
            
            .hero p {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <!-- 粒子背景 -->
    <div id="particles-js"></div>
    
    <!-- 导航栏 -->
    <header>
        <div class="container">
            <nav class="navbar">
                <div class="logo">EX<span>跨境云支付平台</span></div>
                <ul class="nav-links">
                    <li><a href="#">首页</a></li>
                    <li><a href="#">解决方案</a></li>
                    <li><a href="#">安全合规</a></li>
                    <li><a href="#">帮助中心</a></li>
                    <li><a href="#">关于我们</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- 主横幅 -->
    <section class="hero">
        <div class="digital-circle"></div>
        <div class="digital-circle"></div>
        <div class="container">
            <div class="hero-content">
                <h1 class="floating">革新全球支付 · 连接无界商机</h1>
                <p>EX跨境云支付平台以科技赋能全球支付，为跨境企业、支付公司和银行提供安全、高效、灵活的全球资金流转与管理解决方案</p>
                <a href="#" class="btn">立即咨询</a>
            </div>
        </div>
    </section>

    <!-- 核心优势 -->
    <section class="features">
        <div class="container">
            <div class="section-title">
                <h2>我们的核心优势</h2>
                <p>以新加坡为基地，打造面向未来的支付基础设施</p>
            </div>
            <div class="feature-grid">
                <div class="feature-item">
                    <h3><i class="fas fa-globe-asia"></i>跨境认知深厚</h3>
                    <p>团队拥有丰富的全球支付、合规和金融科技经验，深度理解各国监管与业务场景，助力客户无障碍开展跨境业务。</p>
                </div>
                <div class="feature-item">
                    <h3><i class="fas fa-microchip"></i>科技赋能</h3>
                    <p>云原生、微服务架构，支持多币种、多通道，提供多种集成模式（白牌模式、API对接、本地化部署），实现灵活定制，助力客户快速上线。</p>
                </div>
                <div class="feature-item">
                    <h3><i class="fas fa-shield-alt"></i>安全保障</h3>
                    <p>全链路加密、智能风控引擎、合规审计，多重保障每一笔交易的安全与合规，为客户降低风险。</p>
                </div>
                <div class="feature-item">
                    <h3><i class="fas fa-chart-line"></i>资本与资源</h3>
                    <p>背靠Kairous等顶级科创基金，链接东南亚及全球金融网络，为客户提供强大的资源支持和国际市场拓展能力。</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 全球覆盖网络 -->
    <section class="global-network">
        <div class="container">
            <div class="section-title">
                <h2>跨境认知深厚</h2>
                <p>EurewaX平台连接全球主要金融中心，覆盖主要经济和支付市场</p>
            </div>
            <div class="world-map-container">
                <div class="map-visual">
                    <div id="globe-container" style="width: 100%; height: 500px; position: relative;">
                        <!-- 3D地球将在这里渲染 -->
                    </div>
                    
                    <!-- Three.js库 -->
                    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
                    
                    <!-- 地球纹理和数据点位置 -->
                    <script>
                        // 初始化Three.js场景
                        const container = document.getElementById('globe-container');
                        const width = container.clientWidth;
                        const height = container.clientHeight;
                        
                        // 创建场景
                        const scene = new THREE.Scene();
                        
                        // 创建相机
                        const camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000);
                        camera.position.z = 300;
                        
                        // 创建渲染器
                        const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
                        renderer.setSize(width, height);
                        renderer.setClearColor(0x000000, 0); // 透明背景
                        container.appendChild(renderer.domElement);
                        
                        // 创建科技感点阵地球 (Stripe风格)
                        const radius = 100;
                        const segments = 64;
                        
                        // 创建基础球体
                        const baseGeometry = new THREE.SphereGeometry(radius, segments, segments);
                        const baseMaterial = new THREE.MeshBasicMaterial({
                            color: 0x000000,
                            transparent: true,
                            opacity: 0.1
                        });
                        const baseMesh = new THREE.Mesh(baseGeometry, baseMaterial);
                        scene.add(baseMesh);
                        
                        // 创建点阵地球
                        const dotDensity = 2.5; // 点的密度
                        const dotSize = 0.6;    // 点的大小
                        
                        // 创建点云几何体
                        const dotGeometry = new THREE.BufferGeometry();
                        const positions = [];
                        const colors = [];
                        const sizes = [];
                        
                        // 生成随机点
                        const color1 = new THREE.Color(0x00e5ff); // 蓝色
                        const color2 = new THREE.Color(0x2565f7); // 深蓝色
                        
                        // 创建均匀分布在球面上的点
                        for (let i = 0; i < segments * dotDensity; i++) {
                            for (let j = 0; j < segments * dotDensity / 2; j++) {
                                // 球面坐标
                                const theta = (i / (segments * dotDensity)) * Math.PI * 2;
                                const phi = (j / (segments * dotDensity / 2)) * Math.PI;
                                
                                // 随机偏移，使点看起来更自然
                                const randomOffset = Math.random() * 0.02;
                                
                                // 转换为笛卡尔坐标
                                const x = -radius * Math.sin(phi) * Math.cos(theta);
                                const y = radius * Math.cos(phi);
                                const z = radius * Math.sin(phi) * Math.sin(theta);
                                
                                // 随机决定是否显示这个点（创造稀疏效果）
                                if (Math.random() > 0.75) {
                                    positions.push(x, y, z);
                                    
                                    // 颜色渐变
                                    const mixRatio = Math.random();
                                    const color = new THREE.Color().lerpColors(color1, color2, mixRatio);
                                    colors.push(color.r, color.g, color.b);
                                    
                                    // 随机点大小
                                    sizes.push(dotSize * (0.5 + Math.random() * 0.5));
                                }
                            }
                        }
                        
                        // 设置点云属性
                        dotGeometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
                        dotGeometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
                        dotGeometry.setAttribute('size', new THREE.Float32BufferAttribute(sizes, 1));
                        
                        // 创建点云材质
                        const dotMaterial = new THREE.PointsMaterial({
                            size: dotSize,
                            vertexColors: true,
                            transparent: true,
                            opacity: 0.8,
                            sizeAttenuation: true
                        });
                        
                        // 创建点云
                        const dotCloud = new THREE.Points(dotGeometry, dotMaterial);
                        scene.add(dotCloud);
                        
                        // 创建地球外围光晕
                        const glowGeometry = new THREE.SphereGeometry(radius * 1.1, segments, segments);
                        const glowMaterial = new THREE.ShaderMaterial({
                            uniforms: {
                                glowColor: { value: new THREE.Color(0x2565f7) },
                                innerRadius: { value: radius },
                                outerRadius: { value: radius * 1.2 }
                            },
                            vertexShader: `
                                varying vec3 vNormal;
                                varying vec3 vPosition;
                                void main() {
                                    vNormal = normalize(normalMatrix * normal);
                                    vPosition = (modelViewMatrix * vec4(position, 1.0)).xyz;
                                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                                }
                            `,
                            fragmentShader: `
                                uniform vec3 glowColor;
                                uniform float innerRadius;
                                uniform float outerRadius;
                                varying vec3 vNormal;
                                varying vec3 vPosition;
                                void main() {
                                    float intensity = pow(0.75 - dot(vNormal, vec3(0, 0, 1.0)), 2.0);
                                    gl_FragColor = vec4(glowColor, intensity * 0.12);
                                }
                            `,
                            side: THREE.BackSide,
                            blending: THREE.AdditiveBlending,
                            transparent: true
                        });
                        
                        const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
                        scene.add(glowMesh);
                        
                        // 定义全球主要金融中心的坐标
                        const locations = [
                            { name: '北美', lat: 40, lon: -100, color: 0x00e5ff },
                            { name: '欧洲', lat: 50, lon: 10, color: 0x00e5ff },
                            { name: '中欧', lat: 45, lon: 20, color: 0x00e5ff },
                            { name: '东南亚', lat: 10, lon: 110, color: 0x00e5ff },
                            { name: '日本', lat: 35, lon: 140, color: 0x00e5ff },
                            { name: '拉美', lat: -15, lon: -60, color: 0x00e5ff },
                            { name: '非洲', lat: 0, lon: 20, color: 0x00e5ff },
                            { name: '大洋洲', lat: -25, lon: 135, color: 0x00e5ff },
                        ];
                        
                        // EurewaX中心位置
                        const eurewaXCenter = { lat: 1.3, lon: 103.8, color: 0x2565f7 }; // 新加坡坐标
                        
                        // 将经纬度转换为3D坐标
                        function latLonToVector3(lat, lon, radius) {
                            const phi = (90 - lat) * Math.PI / 180;
                            const theta = (lon + 180) * Math.PI / 180;
                            
                            const x = -radius * Math.sin(phi) * Math.cos(theta);
                            const y = radius * Math.cos(phi);
                            const z = radius * Math.sin(phi) * Math.sin(theta);
                            
                            return new THREE.Vector3(x, y, z);
                        }
                        
                        // 创建点位标记
                        const pointsGroup = new THREE.Group();
                        scene.add(pointsGroup);
                        
                        // 添加EurewaX中心点 - 更科技感设计
                        const eurewaXPosition = latLonToVector3(eurewaXCenter.lat, eurewaXCenter.lon, radius * 1.02);
                        
                        // 创建核心点
                        const eurewaXGeometry = new THREE.SphereGeometry(2.5, 16, 16);
                        const eurewaXMaterial = new THREE.MeshBasicMaterial({ 
                            color: 0x2565f7,
                            emissive: 0x2565f7,
                            emissiveIntensity: 1.0
                        });
                        const eurewaXMesh = new THREE.Mesh(eurewaXGeometry, eurewaXMaterial);
                        eurewaXMesh.position.copy(eurewaXPosition);
                        pointsGroup.add(eurewaXMesh);
                        
                        // 添加脉冲效果
                        const pulseGeometry = new THREE.SphereGeometry(2.5, 16, 16);
                        const pulseMaterial = new THREE.ShaderMaterial({
                            uniforms: {
                                color: { value: new THREE.Color(0x00e5ff) },
                                time: { value: 0 }
                            },
                            vertexShader: `
                                varying vec3 vNormal;
                                void main() {
                                    vNormal = normalize(normalMatrix * normal);
                                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                                }
                            `,
                            fragmentShader: `
                                uniform vec3 color;
                                uniform float time;
                                varying vec3 vNormal;
                                void main() {
                                    float intensity = pow(0.7 - dot(vNormal, vec3(0, 0, 1.0)), 2.0);
                                    float pulse = sin(time * 3.0) * 0.5 + 0.5;
                                    gl_FragColor = vec4(color, intensity * pulse * 0.8);
                                }
                            `,
                            transparent: true,
                            blending: THREE.AdditiveBlending
                        });
                        
                        const pulseMesh = new THREE.Mesh(pulseGeometry, pulseMaterial);
                        pulseMesh.position.copy(eurewaXPosition);
                        pulseMesh.scale.set(1.2, 1.2, 1.2);
                        pointsGroup.add(pulseMesh);
                        
                        // 添加多层光环
                        const ringCount = 3;
                        const rings = [];
                        
                        for (let i = 0; i < ringCount; i++) {
                            const ringSize = 4 + i * 2;
                            const ringGeometry = new THREE.RingGeometry(ringSize, ringSize + 0.5, 32);
                            const ringMaterial = new THREE.MeshBasicMaterial({ 
                                color: i % 2 === 0 ? 0x00e5ff : 0x2565f7, 
                                side: THREE.DoubleSide,
                                transparent: true,
                                opacity: 0.7 - (i * 0.15)
                            });
                            
                            const ring = new THREE.Mesh(ringGeometry, ringMaterial);
                            ring.position.copy(eurewaXPosition);
                            ring.lookAt(camera.position);
                            ring.userData = { rotationSpeed: 0.002 + (i * 0.001) };
                            
                            pointsGroup.add(ring);
                            rings.push(ring);
                        }
                        
                        // 存储到全局变量以便动画
                        window.eurewaXElements = {
                            pulseMesh,
                            rings
                        };
                        
                        // 添加其他金融中心点
                        const locationPoints = [];
                        const connectionLines = [];
                        
                        locations.forEach(location => {
                            const position = latLonToVector3(location.lat, location.lon, radius * 1.02);
                            
                            // 创建点 - 科技感设计
                            const pointGeometry = new THREE.SphereGeometry(1.2, 16, 16);
                            const pointMaterial = new THREE.MeshBasicMaterial({ 
                                color: location.color,
                                transparent: true,
                                opacity: 0.9
                            });
                            const pointMesh = new THREE.Mesh(pointGeometry, pointMaterial);
                            pointMesh.position.copy(position);
                            
                            // 添加光晕效果
                            const haloGeometry = new THREE.SphereGeometry(1.8, 16, 16);
                            const haloMaterial = new THREE.ShaderMaterial({
                                uniforms: {
                                    color: { value: new THREE.Color(location.color) }
                                },
                                vertexShader: `
                                    varying vec3 vNormal;
                                    void main() {
                                        vNormal = normalize(normalMatrix * normal);
                                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                                    }
                                `,
                                fragmentShader: `
                                    uniform vec3 color;
                                    varying vec3 vNormal;
                                    void main() {
                                        float intensity = pow(0.7 - dot(vNormal, vec3(0, 0, 1.0)), 2.0);
                                        gl_FragColor = vec4(color, intensity * 0.5);
                                    }
                                `,
                                transparent: true,
                                blending: THREE.AdditiveBlending
                            });
                            
                            const halo = new THREE.Mesh(haloGeometry, haloMaterial);
                            halo.position.copy(position);
                            pointsGroup.add(halo);
                            
                            pointsGroup.add(pointMesh);
                            locationPoints.push(pointMesh);
                            
                            // 创建科技感连接线
                            const linePoints = 100; // 更多的点使曲线更平滑
                            const linePositions = new Float32Array(linePoints * 3);
                            const lineColors = new Float32Array(linePoints * 3);
                            
                            // 创建曲线路径
                            const curve = new THREE.QuadraticBezierCurve3(
                                eurewaXPosition,
                                new THREE.Vector3(
                                    (eurewaXPosition.x + position.x) * 0.5,
                                    (eurewaXPosition.y + position.y) * 0.5,
                                    (eurewaXPosition.z + position.z) * 0.5 + 20
                                ),
                                position
                            );
                            
                            const curvePoints = curve.getPoints(linePoints - 1);
                            
                            // 设置点位置和颜色
                            for (let i = 0; i < linePoints; i++) {
                                const point = curvePoints[i];
                                linePositions[i * 3] = point.x;
                                linePositions[i * 3 + 1] = point.y;
                                linePositions[i * 3 + 2] = point.z;
                                
                                // 颜色渐变从蓝到浅蓝
                                const ratio = i / (linePoints - 1);
                                const color1 = new THREE.Color(0x2565f7); // 深蓝
                                const color2 = new THREE.Color(0x00e5ff); // 浅蓝
                                const color = new THREE.Color().lerpColors(color1, color2, ratio);
                                
                                lineColors[i * 3] = color.r;
                                lineColors[i * 3 + 1] = color.g;
                                lineColors[i * 3 + 2] = color.b;
                            }
                            
                            const lineGeometry = new THREE.BufferGeometry();
                            lineGeometry.setAttribute('position', new THREE.BufferAttribute(linePositions, 3));
                            lineGeometry.setAttribute('color', new THREE.BufferAttribute(lineColors, 3));
                            
                            const lineMaterial = new THREE.LineBasicMaterial({ 
                                vertexColors: true,
                                transparent: true,
                                opacity: 0.7,
                                linewidth: 1
                            });
                            
                            const line = new THREE.Line(lineGeometry, lineMaterial);
                            pointsGroup.add(line);
                            connectionLines.push(line);
                        });
                        
                        // 添加科技感动画粒子
                        const particlesGroup = new THREE.Group();
                        scene.add(particlesGroup);
                        
                        // 每条线添加多个粒子
                        connectionLines.forEach((line, index) => {
                            // 每条线上添加多个粒子
                            const particleCount = 3 + Math.floor(Math.random() * 3); // 3-5个粒子
                            
                            for (let i = 0; i < particleCount; i++) {
                                // 使用点精灵材质代替球体
                                const particleGeometry = new THREE.BufferGeometry();
                                particleGeometry.setAttribute('position', new THREE.Float32BufferAttribute([0, 0, 0], 3));
                                
                                // 创建自定义精灵材质
                                const particleMaterial = new THREE.PointsMaterial({ 
                                    color: Math.random() > 0.5 ? 0x00e5ff : 0x2565f7,
                                    size: 1.5 + Math.random() * 1.0,
                                    transparent: true,
                                    opacity: 0.8,
                                    blending: THREE.AdditiveBlending
                                });
                                
                                const particle = new THREE.Points(particleGeometry, particleMaterial);
                                particlesGroup.add(particle);
                                
                                // 存储粒子数据
                                particle.userData = {
                                    lineIndex: index,
                                    progress: Math.random(), // 随机初始位置
                                    speed: 0.003 + Math.random() * 0.007, // 随机速度
                                    size: particleMaterial.size,
                                    pulseSpeed: 0.05 + Math.random() * 0.05, // 脉冲速度
                                    pulseTime: Math.random() * Math.PI * 2 // 随机相位
                                };
                            }
                        });
                        
                        // 科技感动画循环
                        function animate() {
                            requestAnimationFrame(animate);
                            
                            // 更新时间变量用于着色器
                            const time = performance.now() * 0.001; // 秒
                            
                            // 旋转点阵地球
                            dotCloud.rotation.y += 0.0005;
                            baseMesh.rotation.y += 0.0005;
                            glowMesh.rotation.y += 0.0005;
                            
                            // 点位组跟随地球旋转
                            pointsGroup.rotation.y = dotCloud.rotation.y;
                            
                            // 更新EurewaX中心点的脉冲效果
                            if (window.eurewaXElements && window.eurewaXElements.pulseMesh) {
                                const pulseMesh = window.eurewaXElements.pulseMesh;
                                pulseMesh.material.uniforms.time.value = time;
                                
                                // 旋转多层光环
                                if (window.eurewaXElements.rings) {
                                    window.eurewaXElements.rings.forEach(ring => {
                                        ring.rotation.z += ring.userData.rotationSpeed;
                                        ring.lookAt(camera.position);
                                    });
                                }
                            }
                            
                            // 更新粒子位置和大小
                            particlesGroup.children.forEach(particle => {
                                const { lineIndex, progress, speed, pulseSpeed, pulseTime } = particle.userData;
                                const line = connectionLines[lineIndex];
                                
                                // 更新进度
                                particle.userData.progress += speed;
                                if (particle.userData.progress > 1) {
                                    particle.userData.progress = 0;
                                }
                                
                                // 更新脉冲时间
                                particle.userData.pulseTime += pulseSpeed;
                                
                                // 脉冲效果计算
                                const pulse = Math.sin(particle.userData.pulseTime) * 0.5 + 0.5;
                                particle.material.size = particle.userData.size * (0.8 + pulse * 0.4);
                                particle.material.opacity = 0.6 + pulse * 0.4;
                                
                                // 获取线条上的点
                                const linePoints = line.geometry.attributes.position;
                                const pointIndex = Math.floor(particle.userData.progress * (linePoints.count - 1));
                                
                                // 设置粒子位置
                                if (pointIndex < linePoints.count) {
                                    const x = linePoints.array[pointIndex * 3];
                                    const y = linePoints.array[pointIndex * 3 + 1];
                                    const z = linePoints.array[pointIndex * 3 + 2];
                                    
                                    // 更新粒子几何体的位置
                                    const positions = particle.geometry.attributes.position.array;
                                    positions[0] = x;
                                    positions[1] = y;
                                    positions[2] = z;
                                    particle.geometry.attributes.position.needsUpdate = true;
                                }
                            });
                            
                            // 添加网格点的闪烁效果
                            if (dotCloud.geometry.attributes.size) {
                                const sizes = dotCloud.geometry.attributes.size.array;
                                for (let i = 0; i < sizes.length; i++) {
                                    const originalSize = sizes[i];
                                    // 使用不同相位的正弦波形成闪烁效果
                                    const flicker = Math.sin(time * 2 + i * 100) * 0.1 + 0.9;
                                    sizes[i] = originalSize * flicker;
                                }
                                dotCloud.geometry.attributes.size.needsUpdate = true;
                            }
                            
                            renderer.render(scene, camera);
                        }
                        
                        // 处理窗口大小调整
                        window.addEventListener('resize', () => {
                            const newWidth = container.clientWidth;
                            const newHeight = container.clientHeight;
                            
                            camera.aspect = newWidth / newHeight;
                            camera.updateProjectionMatrix();
                            
                            renderer.setSize(newWidth, newHeight);
                        });
                        
                        // 启动动画
                        animate();
                    </script>
                    
                    <!-- 统计数据 -->
                    <div class="coverage-stats">
                        <div class="coverage-item">
                            <span class="coverage-number">260<sup>+</sup></span>
                            <span class="coverage-label">银行合作伙伴</span>
                        </div>
                        <div class="coverage-item">
                            <span class="coverage-number">35<sup>+</sup></span>
                            <span class="coverage-label">国家和地区</span>
                        </div>
                        <div class="coverage-item">
                            <span class="coverage-number">150<sup>+</sup></span>
                            <span class="coverage-label">支付方式</span>
                        </div>
                    </div>
                </div>
                
                <div class="map-description">
                    <h3>全球金融网络深度互联</h3>
                    <p>通过 EurewaX 跨境云支付平台，客户可以便捷连接全球各地金融中心，实现无缝跨境支付和清算。我们已在主要区域市场建立了完善的合规和运营体系，让您的全球化业务快速落地。</p>
                    
                    <div class="compliance-info">
                        <div class="compliance-item">
                            <i class="fas fa-check-circle"></i>
                            <span>全球合规认证</span>
                        </div>
                        <div class="compliance-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>本地化风控系统</span>
                        </div>
                        <div class="compliance-item">
                            <i class="fas fa-clock"></i>
                            <span>24/7 全球监控</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 科技赋能部分 -->
    <section class="tech-empowerment">
        <div class="container">
            <div class="section-title">
                <h2>科技赋能</h2>
                <p>云原生微服务架构，弹性扩展，快速集成</p>
            </div>
            <div class="tech-container">
                <div class="tech-diagram">
                    <svg viewBox="0 0 800 450" class="microservice-diagram">
                        <!-- 整体背景 -->
                        <rect x="50" y="50" width="700" height="350" fill="rgba(0,12,36,0.7)" rx="10" ry="10" />
                        
                        <!-- 横向网格线 -->
                        <line x1="50" y1="150" x2="750" y2="150" stroke="rgba(255,255,255,0.05)" stroke-width="1" />
                        <line x1="50" y1="250" x2="750" y2="250" stroke="rgba(255,255,255,0.05)" stroke-width="1" />
                        <line x1="50" y1="350" x2="750" y2="350" stroke="rgba(255,255,255,0.05)" stroke-width="1" />
                        
                        <!-- 纵向网格线 -->
                        <line x1="200" y1="50" x2="200" y2="400" stroke="rgba(255,255,255,0.05)" stroke-width="1" />
                        <line x1="400" y1="50" x2="400" y2="400" stroke="rgba(255,255,255,0.05)" stroke-width="1" />
                        <line x1="600" y1="50" x2="600" y2="400" stroke="rgba(255,255,255,0.05)" stroke-width="1" />
                        
                        <!-- 标题区 -->
                        <text x="400" y="80" text-anchor="middle" fill="#fff" font-size="16" font-weight="bold">微服务架构与多种集成模式</text>
                        
                        <!-- API集成部分 -->
                        <rect x="100" y="120" width="160" height="70" rx="8" ry="8" fill="rgba(37, 101, 247, 0.2)" stroke="#2565f7" stroke-width="1" class="service-box" />
                        <text x="180" y="145" text-anchor="middle" fill="#fff" font-size="14">API集成</text>
                        <text x="180" y="170" text-anchor="middle" fill="rgba(255,255,255,0.7)" font-size="11">灵活定制的接入方式</text>
                        
                        <!-- 白牌模式部分 -->
                        <rect x="320" y="120" width="160" height="70" rx="8" ry="8" fill="rgba(37, 101, 247, 0.2)" stroke="#2565f7" stroke-width="1" class="service-box" />
                        <text x="400" y="145" text-anchor="middle" fill="#fff" font-size="14">白牌模式</text>
                        <text x="400" y="170" text-anchor="middle" fill="rgba(255,255,255,0.7)" font-size="11">完全定制品牌和界面</text>
                        
                        <!-- 本地化部署部分 -->
                        <rect x="540" y="120" width="160" height="70" rx="8" ry="8" fill="rgba(37, 101, 247, 0.2)" stroke="#2565f7" stroke-width="1" class="service-box" />
                        <text x="620" y="145" text-anchor="middle" fill="#fff" font-size="14">本地化部署</text>
                        <text x="620" y="170" text-anchor="middle" fill="rgba(255,255,255,0.7)" font-size="11">满足严格合规要求</text>
                        
                        <!-- 中心服务部分 -->
                        <circle cx="400" cy="250" r="40" fill="rgba(0, 229, 255, 0.2)" stroke="#00e5ff" stroke-width="2" filter="url(#glow-tech)" class="core-service" />
                        <text x="400" y="243" text-anchor="middle" fill="#fff" font-size="14">核心业务</text>
                        <text x="400" y="263" text-anchor="middle" fill="#fff" font-size="14">微服务</text>
                        
                        <!-- 微服务节点 -->
                        <circle cx="300" cy="200" r="20" fill="rgba(0, 229, 255, 0.15)" stroke="#00e5ff" stroke-width="1" class="service-node" />
                        <text x="300" y="205" text-anchor="middle" fill="#fff" font-size="10">账户管理</text>
                        
                        <circle cx="340" cy="280" r="20" fill="rgba(0, 229, 255, 0.15)" stroke="#00e5ff" stroke-width="1" class="service-node" />
                        <text x="340" y="285" text-anchor="middle" fill="#fff" font-size="10">币种管理</text>
                        
                        <circle cx="460" cy="280" r="20" fill="rgba(0, 229, 255, 0.15)" stroke="#00e5ff" stroke-width="1" class="service-node" />
                        <text x="460" y="285" text-anchor="middle" fill="#fff" font-size="10">清算服务</text>
                        
                        <circle cx="500" cy="200" r="20" fill="rgba(0, 229, 255, 0.15)" stroke="#00e5ff" stroke-width="1" class="service-node" />
                        <text x="500" y="205" text-anchor="middle" fill="#fff" font-size="10">风控服务</text>
                        
                        <!-- 连接线 -->
                        <line x1="300" y1="200" x2="370" y2="230" stroke="#00e5ff" stroke-width="1" stroke-opacity="0.6" class="service-line" />
                        <line x1="340" y1="280" x2="370" y2="270" stroke="#00e5ff" stroke-width="1" stroke-opacity="0.6" class="service-line" />
                        <line x1="460" y1="280" x2="430" y2="270" stroke="#00e5ff" stroke-width="1" stroke-opacity="0.6" class="service-line" />
                        <line x1="500" y1="200" x2="430" y2="230" stroke="#00e5ff" stroke-width="1" stroke-opacity="0.6" class="service-line" />
                        
                        <!-- 下层服务 -->
                        <rect x="160" y="330" width="120" height="40" rx="5" ry="5" fill="rgba(37, 101, 247, 0.1)" stroke="rgba(37, 101, 247, 0.5)" stroke-width="1" class="infra-service" />
                        <text x="220" y="355" text-anchor="middle" fill="#fff" font-size="12">多币种支持</text>
                        
                        <rect x="340" y="330" width="120" height="40" rx="5" ry="5" fill="rgba(37, 101, 247, 0.1)" stroke="rgba(37, 101, 247, 0.5)" stroke-width="1" class="infra-service" />
                        <text x="400" y="355" text-anchor="middle" fill="#fff" font-size="12">弹性扩展</text>
                        
                        <rect x="520" y="330" width="120" height="40" rx="5" ry="5" fill="rgba(37, 101, 247, 0.1)" stroke="rgba(37, 101, 247, 0.5)" stroke-width="1" class="infra-service" />
                        <text x="580" y="355" text-anchor="middle" fill="#fff" font-size="12">多通道支持</text>
                        
                        <!-- 连接线 -->
                        <line x1="300" y1="220" x2="220" y2="330" stroke="rgba(37, 101, 247, 0.5)" stroke-width="1" stroke-dasharray="3,2" class="service-line" />
                        <line x1="376" y1="286" x2="376" y2="330" stroke="rgba(37, 101, 247, 0.5)" stroke-width="1" stroke-dasharray="3,2" class="service-line" />
                        <line x1="460" y1="300" x2="520" y2="330" stroke="rgba(37, 101, 247, 0.5)" stroke-width="1" stroke-dasharray="3,2" class="service-line" />
                        
                        <!-- 效果 -->
                        <defs>
                            <filter id="glow-tech" x="-50%" y="-50%" width="200%" height="200%">
                                <feGaussianBlur stdDeviation="2" result="blur" />
                                <feFlood flood-color="#00e5ff" flood-opacity="0.3" result="color" />
                                <feComposite in="color" in2="blur" operator="in" result="shadow" />
                                <feMerge>
                                    <feMergeNode in="shadow" />
                                    <feMergeNode in="SourceGraphic" />
                                </feMerge>
                            </filter>
                        </defs>
                    </svg>
                </div>
                
                <div class="tech-features">
                    <h3>先进技术架构</h3>
                    <ul class="feature-list">
                        <li>
                            <span class="feature-icon"><i class="fas fa-cloud"></i></span>
                            <div class="feature-content">
                                <h4>云原生设计</h4>
                                <p>采用容器化应用程序和自动化运维，确保99.99%的服务可用性</p>
                            </div>
                        </li>
                        <li>
                            <span class="feature-icon"><i class="fas fa-code-branch"></i></span>
                            <div class="feature-content">
                                <h4>微服务架构</h4>
                                <p>独立部署的动态可扩展模块，快速适应业务需求变化</p>
                            </div>
                        </li>
                        <li>
                            <span class="feature-icon"><i class="fas fa-plug"></i></span>
                            <div class="feature-content">
                                <h4>多种集成方式</h4>
                                <p>提供API集成、白牌模式和本地化部署选项，满足不同客户需求</p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 安全保障部分 -->
    <section class="security-assurance">
        <div class="container">
            <div class="section-title">
                <h2>安全保障</h2>
                <p>服务全生命周期的安全与合规面面保障</p>
            </div>
            <div class="security-container">
                <div class="security-layers">
                    <svg viewBox="0 0 800 500" class="security-diagram">
                        <!-- 背景 -->
                        <defs>
                            <linearGradient id="securityGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" stop-color="rgba(4, 13, 33, 0.9)" />
                                <stop offset="100%" stop-color="rgba(11, 49, 160, 0.4)" />
                            </linearGradient>
                            <filter id="glow-security" x="-30%" y="-30%" width="160%" height="160%">
                                <feGaussianBlur stdDeviation="3" result="blur" />
                                <feFlood flood-color="#00e5ff" flood-opacity="0.2" result="color" />
                                <feComposite in="color" in2="blur" operator="in" result="shadow" />
                                <feMerge>
                                    <feMergeNode in="shadow" />
                                    <feMergeNode in="SourceGraphic" />
                                </feMerge>
                            </filter>
                        </defs>
                        
                        <!-- 主背景 -->
                        <rect x="50" y="50" width="700" height="400" fill="url(#securityGradient)" rx="15" ry="15" />
                        
                        <!-- 安全区域分层 -->
                        <path d="M400,100 Q580,150 700,240 L700,450 L100,450 L100,240 Q220,150 400,100 Z" fill="rgba(37, 101, 247, 0.05)" stroke="rgba(0, 229, 255, 0.15)" stroke-width="1" />
                        <path d="M400,160 Q550,200 640,260 L640,430 L160,430 L160,260 Q250,200 400,160 Z" fill="rgba(37, 101, 247, 0.08)" stroke="rgba(0, 229, 255, 0.2)" stroke-width="1" />
                        <path d="M400,220 Q520,250 580,290 L580,400 L220,400 L220,290 Q280,250 400,220 Z" fill="rgba(37, 101, 247, 0.12)" stroke="rgba(0, 229, 255, 0.3)" stroke-width="1" />
                        
                        <!-- 中心监控区域 -->
                        <circle cx="400" cy="300" r="60" fill="rgba(0, 229, 255, 0.1)" stroke="rgba(0, 229, 255, 0.6)" stroke-width="1.5" filter="url(#glow-security)" class="pulse-circle" />
                        <text x="400" y="290" text-anchor="middle" fill="#fff" font-size="16" font-weight="bold">实时安全</text>
                        <text x="400" y="310" text-anchor="middle" fill="#fff" font-size="16" font-weight="bold">监控中心</text>
                        
                        <!-- 四层防护标签 -->
                        <text x="235" y="255" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-size="14" transform="rotate(-8,235,255)">应用层防护</text>
                        <text x="570" y="255" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-size="14" transform="rotate(8,570,255)">数据层防护</text>
                        <text x="400" y="190" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-size="14">网络层防护</text>
                        <text x="400" y="405" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-size="14">合规层防护</text>
                        
                        <!-- 安全图标 -->
                        <circle cx="150" cy="200" r="25" fill="rgba(0, 229, 255, 0.1)" stroke="rgba(0, 229, 255, 0.5)" stroke-width="1" class="security-point" />
                        <text x="150" y="205" text-anchor="middle" fill="#fff" font-size="20">🔒</text>
                        
                        <circle cx="650" cy="200" r="25" fill="rgba(0, 229, 255, 0.1)" stroke="rgba(0, 229, 255, 0.5)" stroke-width="1" class="security-point" />
                        <text x="650" y="205" text-anchor="middle" fill="#fff" font-size="20">🛡️</text>
                        
                        <circle cx="200" cy="400" r="25" fill="rgba(0, 229, 255, 0.1)" stroke="rgba(0, 229, 255, 0.5)" stroke-width="1" class="security-point" />
                        <text x="200" y="405" text-anchor="middle" fill="#fff" font-size="20">📝</text>
                        
                        <circle cx="600" cy="400" r="25" fill="rgba(0, 229, 255, 0.1)" stroke="rgba(0, 229, 255, 0.5)" stroke-width="1" class="security-point" />
                        <text x="600" y="405" text-anchor="middle" fill="#fff" font-size="20">🔎</text>
                        
                        <!-- 扫描线效果 -->
                        <line x1="50" y1="250" x2="750" y2="250" stroke="rgba(0, 229, 255, 0.7)" stroke-width="1" stroke-dasharray="5,5" class="scan-line" />
                        
                        <!-- 安全信息点 -->
                        <circle cx="300" cy="150" r="5" fill="#2565f7" class="data-point" />
                        <circle cx="480" cy="180" r="5" fill="#2565f7" class="data-point" />
                        <circle cx="230" cy="350" r="5" fill="#2565f7" class="data-point" />
                        <circle cx="550" cy="320" r="5" fill="#2565f7" class="data-point" />
                        <circle cx="350" cy="420" r="5" fill="#2565f7" class="data-point" />
                        <circle cx="450" cy="420" r="5" fill="#2565f7" class="data-point" />
                    </svg>
                </div>
                
                <div class="security-features">
                    <h3>全方位安全防护</h3>
                    <div class="security-feature-grid">
                        <div class="security-feature-item">
                            <div class="security-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div class="security-text">
                                <h4>全链路加密</h4>
                                <p>采用银行级加密标准，保护敏感支付数据</p>
                            </div>
                        </div>
                        <div class="security-feature-item">
                            <div class="security-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="security-text">
                                <h4>智能风控引擎</h4>
                                <p>实时监测交易异常，自动化风险识别与预警</p>
                            </div>
                        </div>
                        <div class="security-feature-item">
                            <div class="security-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="security-text">
                                <h4>合规审计系统</h4>
                                <p>满足各国监管要求，自动生成合规报表</p>
                            </div>
                        </div>
                        <div class="security-feature-item">
                            <div class="security-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="security-text">
                                <h4>数据全生命周期保护</h4>
                                <p>多级数据备份和灾难恢复方案</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 解决方案优势 -->
    <section class="clients">
        <div class="container">
            <div class="section-title">
                <h2>解决方案优势</h2>
                <p>解决跨境支付中的四大核心挑战</p>
            </div>
            <div class="client-types">
                <div class="client-card">
                    <div class="client-icon">💻</div>
                    <h3>科技投入不足</h3>
                    <p>提供多种集成方式（白牌模式、API对接、本地化部署），满足不同客户需求，无需大量研发投入即可接入全球支付通道</p>
                </div>
                <div class="client-card">
                    <div class="client-icon">🌐</div>
                    <h3>金融资源匮乏</h3>
                    <p>整合全球优质金融资源，提供多币种、多牌照支持，降低准入门槛</p>
                </div>
                <div class="client-card">
                    <div class="client-icon">🔒</div>
                    <h3>安全能力短板</h3>
                    <p>专业风控团队和系统，全方位保障交易安全，符合全球各地区合规要求</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 架构图部分 -->
    <section class="architecture">
        <div class="container">
            <div class="section-title">
                <h2>业务架构图</h2>
                <p>全方位展示EurewaX跨境云支付平台的系统架构</p>
            </div>
            <div class="architecture-content">
                <div class="architecture-diagram-wrapper">
                    <div class="tech-line"></div>
                    <div class="business-architecture">
                        <style>
                            .business-architecture {
                                width: 100%;
                                position: relative;
                                background: rgba(4, 13, 33, 0.3);
                                border-radius: 15px;
                                padding: 20px;
                                margin-top: 20px;
                                border: 1px solid rgba(37, 101, 247, 0.2);
                            }
                            
                            .arch-layer {
                                margin-bottom: 20px;
                                position: relative;
                            }
                            
                            .arch-layer-title {
                                text-align: center;
                                padding: 8px;
                                color: #fff;
                                font-size: 18px;
                                font-weight: 600;
                                margin-bottom: 10px;
                                position: relative;
                            }
                            
                            .arch-layer-title::after {
                                content: '';
                                position: absolute;
                                bottom: -2px;
                                left: 50%;
                                transform: translateX(-50%);
                                width: 60px;
                                height: 2px;
                                background: #00e5ff;
                                border-radius: 2px;
                            }
                            
                            .arch-layer-container {
                                background: rgba(255, 255, 255, 0.05);
                                border-radius: 10px;
                                padding: 15px;
                                position: relative;
                                border: 1px solid rgba(255, 255, 255, 0.08);
                            }
                            
                            .arch-upper-layer .arch-layer-container {
                                background: rgba(33, 150, 243, 0.05);
                                border-color: rgba(33, 150, 243, 0.1);
                            }
                            
                            .arch-middle-layer .arch-layer-container {
                                background: rgba(37, 101, 247, 0.08);
                                border-color: rgba(37, 101, 247, 0.15);
                            }
                            
                            .arch-lower-layer .arch-layer-container {
                                background: rgba(76, 175, 80, 0.05);
                                border-color: rgba(76, 175, 80, 0.1);
                            }
                            
                            .arch-entities {
                                display: flex;
                                flex-wrap: wrap;
                                justify-content: center;
                                gap: 15px;
                            }
                            
                            .arch-entity {
                                background: rgba(255, 255, 255, 0.08);
                                border-radius: 8px;
                                padding: 12px;
                                min-width: 150px;
                                flex: 1;
                                text-align: center;
                                position: relative;
                                border: 1px solid rgba(255, 255, 255, 0.1);
                                display: flex;
                                flex-direction: column;
                                align-items: center;
                                justify-content: center;
                                min-height: 80px;
                            }
                            
                            .arch-entity-title {
                                font-size: 14px;
                                font-weight: 600;
                                color: #fff;
                                margin-bottom: 6px;
                            }
                            
                            .arch-entity-desc {
                                font-size: 11px;
                                color: rgba(255, 255, 255, 0.7);
                            }
                            
                            .arch-connector {
                                position: absolute;
                                left: 50%;
                                bottom: -20px;
                                transform: translateX(-50%);
                                width: 2px;
                                height: 20px;
                                background: #00e5ff;
                                z-index: 1;
                            }
                            
                            .arch-connector::before,
                            .arch-connector::after {
                                content: '';
                                position: absolute;
                                width: 6px;
                                height: 6px;
                                border-radius: 50%;
                                background: #00e5ff;
                                box-shadow: 0 0 5px #00e5ff;
                            }
                            
                            .arch-connector::before {
                                bottom: 0;
                                left: 50%;
                                transform: translateX(-50%);
                            }
                            
                            .arch-connector::after {
                                top: 0;
                                left: 50%;
                                transform: translateX(-50%);
                            }
                            
                            .arch-middle-services {
                                display: grid;
                                grid-template-columns: repeat(3, 1fr);
                                gap: 10px;
                                margin-top: 15px;
                            }
                            
                            .arch-service {
                                background: rgba(255, 255, 255, 0.08);
                                border-radius: 8px;
                                padding: 10px;
                                text-align: center;
                                position: relative;
                                border: 1px solid rgba(255, 255, 255, 0.1);
                            }
                            
                            .arch-service-title {
                                font-size: 13px;
                                font-weight: 500;
                                color: #fff;
                                margin-bottom: 4px;
                            }
                            
                            .arch-service-desc {
                                font-size: 10px;
                                color: rgba(255, 255, 255, 0.7);
                            }
                            
                            .arch-platform-logo {
                                position: absolute;
                                top: -22px;
                                left: 50%;
                                transform: translateX(-50%);
                                background: linear-gradient(135deg, #2565f7, #5d38eb);
                                color: #fff;
                                padding: 6px 20px;
                                border-radius: 20px;
                                font-weight: 600;
                                font-size: 14px;
                                box-shadow: 0 3px 10px rgba(37, 101, 247, 0.3);
                                z-index: 10;
                                border: 1px solid rgba(255, 255, 255, 0.2);
                            }
                            
                            .arch-platform-logo span {
                                color: #00e5ff;
                            }
                            
                            @media (max-width: 768px) {
                                .arch-entities {
                                    flex-direction: column;
                                }
                                
                                .arch-entity {
                                    width: 100%;
                                }
                                
                                .arch-middle-services {
                                    grid-template-columns: 1fr;
                                }
                            }
                        </style>
                        
                        <!-- 上层 -->
                        <div class="arch-layer arch-upper-layer">
                            <div class="arch-layer-title">上层 - 客户与合作伙伴</div>
                            <div class="arch-layer-container">
                                <div class="arch-entities">
                                    <div class="arch-entity">
                                        <div class="arch-entity-title">跨境企业</div>
                                        <div class="arch-entity-desc">全球贸易与服务企业</div>
                                    </div>
                                    
                                    <div class="arch-entity">
                                        <div class="arch-entity-title">传统支付服务商</div>
                                        <div class="arch-entity-desc">全球PSP与支付机构</div>
                                    </div>
                                    
                                    <div class="arch-entity">
                                        <div class="arch-entity-title">数字资产支付服务商</div>
                                        <div class="arch-entity-desc">区块链支付与清算服务</div>
                                    </div>
                                    
                                    <div class="arch-entity">
                                        <div class="arch-entity-title">银行</div>
                                        <div class="arch-entity-desc">传统金融机构</div>
                                    </div>
                                </div>
                            </div>
                            <div class="arch-connector"></div>
                        </div>
                        
                        <!-- 中层 -->
                        <div class="arch-layer arch-middle-layer">
                            <div class="arch-platform-logo">EurewaX<span>Cloud</span>Pay</div>
                            <div class="arch-layer-title">中层 - EurewaX跨境云支付平台</div>
                            <div class="arch-layer-container">
                                <div class="arch-entities">
                                    <div class="arch-entity">
                                        <div class="arch-entity-title">多币种收付款</div>
                                        <div class="arch-entity-desc">法定货币与数字资产支付网络</div>
                                    </div>
                                    
                                    <div class="arch-entity">
                                        <div class="arch-entity-title">虚拟卡服务</div>
                                        <div class="arch-entity-desc">U卡与全球支付卡发行管理</div>
                                    </div>
                                    
                                    <div class="arch-entity">
                                        <div class="arch-entity-title">全球收单</div>
                                        <div class="arch-entity-desc">传统与数字资产收银解决方案</div>
                                    </div>
                                </div>
                                
                                <div class="arch-middle-services">
                                    <div class="arch-service">
                                        <div class="arch-service-title">统一账户管理</div>
                                        <div class="arch-service-desc">多币种账户与资产集中管理</div>
                                    </div>
                                    
                                    <div class="arch-service">
                                        <div class="arch-service-title">智能清结算</div>
                                        <div class="arch-service-desc">跨境资金与数字资产清算</div>
                                    </div>
                                    
                                    <div class="arch-service">
                                        <div class="arch-service-title">实时汇率管理</div>
                                        <div class="arch-service-desc">多币种汇率优化与风险管理</div>
                                    </div>
                                    
                                    <div class="arch-service">
                                        <div class="arch-service-title">全球合规体系</div>
                                        <div class="arch-service-desc">跨境支付与资产合规解决方案</div>
                                    </div>
                                    
                                    <div class="arch-service">
                                        <div class="arch-service-title">交易监控分析</div>
                                        <div class="arch-service-desc">全渠道交易监控与数据分析</div>
                                    </div>
                                    
                                    <div class="arch-service">
                                        <div class="arch-service-title">开放平台生态</div>
                                        <div class="arch-service-desc">开放式API与应用生态系统</div>
                                    </div>
                                </div>
                            </div>
                            <div class="arch-connector"></div>
                        </div>
                        
                        <!-- 下层 -->
                        <div class="arch-layer arch-lower-layer">
                            <div class="arch-layer-title">底层 - 金融与服务网络</div>
                            <div class="arch-layer-container">
                                <div class="arch-entities">
                                    <div class="arch-entity">
                                        <div class="arch-entity-title">银行</div>
                                        <div class="arch-entity-desc">全球知名银行 (260+家)</div>
                                    </div>
                                    
                                    <div class="arch-entity">
                                        <div class="arch-entity-title">全球支付服务商</div>
                                        <div class="arch-entity-desc">国际知名PSP (150+支付方式)</div>
                                    </div>
                                    
                                    <div class="arch-entity">
                                        <div class="arch-entity-title">数字资产服务商</div>
                                        <div class="arch-entity-desc">数字资产交易与托管机构</div>
                                    </div>
                                    
                                    <div class="arch-entity">
                                        <div class="arch-entity-title">风控合规服务商</div>
                                        <div class="arch-entity-desc">全球合规与风险管理服务</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 适用客户群体 -->
    <section class="clients">
        <div class="container">
            <div class="section-title">
                <h2>适用客户群体</h2>
                <p>为不同类型的企业提供定制化解决方案</p>
            </div>
            <div class="client-types">
                <div class="client-card">
                    <div class="client-icon">🌏</div>
                    <h3>跨境贸易与服务企业</h3>
                    <p>为全球化企业提供一站式跨境支付解决方案</p>
                </div>
                <div class="client-card">
                    <div class="client-icon">💻</div>
                    <h3>Web2/Web3支付公司</h3>
                    <p>提供安全、合规的支付基础设施和拓展工具</p>
                </div>
                <div class="client-card">
                    <div class="client-icon">🏦</div>
                    <h3>银行及金融机构</h3>
                    <p>助力传统金融机构快速实现数字化转型</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 客户案例 -->
    <section class="case-studies">
        <div class="container">
            <div class="section-title">
                <h2>成功客户案例</h2>
                <p>客户通过EX平台，能够以低至传统成本1/5的投入、高效地开展全球支付业务</p>
            </div>
            
            <div class="case-item">
                <h3>中国知名支付公司跨境业务拓展</h3>
                <p><strong>客户概况：</strong>中国领先的第三方支付服务商，年交易额超过2000亿人民币，寻求快速拓展东南亚、非洲与欧美跨境支付市场</p>
                
                <h4>业务挑战：</h4>
                <ul>
                    <li>各国支付牌照获取周期长、成本高</li>
                    <li>对接多国银行与结算系统技术复杂，需投入大量研发资源</li>
                    <li>新兴市场的合规风险高，自建合规团队需大量时间和资金前期投入</li>
                </ul>
                
                <h4>EX解决方案：</h4>
                <ul>
                    <li>提供全套跨境SaaS支付基础设施，快速对接主流跨境银行和PSP</li>
                    <li>使用SaaS平台，仅需1天时间完成跨境支付基础设施搭建</li>
                    <li>全面使用EX已经过监管验证的全球反洗钱合规系统(AML Shield)</li>
                </ul>
                
                <div class="results">
                    <h4>业务成果：</h4>
                    <ul>
                        <li><strong>合规成本与效果双重优化：</strong>合规成本降低85%，同时品质更优</li>
                        <li><strong>技术成本降低：</strong>技术研发投入降低82%，合规团队缩减70%</li>
                        <li><strong>客户扩展：</strong>1个月内在新市场成功服务超过30家企业客户</li>
                        <li><strong>全球市场覆盖：</strong>从区域性服务商转变为全球支付平台</li>
                    </ul>
                </div>
            </div>
            
            <div class="case-item">
                <h3>新加坡PayFac机构全球化扩张</h3>
                <p><strong>客户概况：</strong>新加坡领先的支付便利商(Payment Facilitator)，专注为中小商户提供一站式支付解决方案，计划从东南亚扩张至全球市场</p>
                
                <h4>业务挑战：</h4>
                <ul>
                    <li>业务能力单一，跨境业务缺失</li>
                    <li>跨境经验缺失，开发盲目投入，风险高</li>
                    <li>跨境合规风控能力不足，无法控制风险</li>
                </ul>
                
                <h4>EX解决方案：</h4>
                <ul>
                    <li>快速对接各地本地银行，超48小时内完成银行对接</li>
                    <li>借助EX智能平台，实现多币种批量快速结算</li>
                    <li>快速接入150+本地支付方式，仅需使用统一API</li>
                </ul>
                
                <div class="results">
                    <h4>业务成果：</h4>
                    <ul>
                        <li><strong>跨境结算成本优化：</strong>交易结算成本节约80%</li>
                        <li><strong>商户覆盖增长：</strong>新增跨境商户10家</li>
                        <li><strong>业务增长：</strong>收入增长率从原有2%提升至5%</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系我们 -->
    <section class="features">
        <div class="container">
            <div class="section-title">
                <h2>联系我们</h2>
                <p>立即联系我们，开启您的全球支付之旅</p>
            </div>
            <div style="text-align: center; margin-top: 30px;">
                <p><strong>公司官网：</strong><a href="https://www.eurewax.com" style="color: var(--primary-color); text-decoration: none;">www.eurewax.com</a></p>
                <p><strong>商务合作：</strong><a href="mailto:<EMAIL>" style="color: var(--primary-color); text-decoration: none;"><EMAIL></a></p>
                <p><strong>地址：</strong>新加坡</p>
                <a href="#" class="btn" style="margin-top: 30px; background-color: var(--primary-color); color: white;">预约演示</a>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="footer-container">
                <div>
                    <div class="footer-logo">EX跨境云支付平台</div>
                    <p>革新全球支付 · 连接无界商机</p>
                </div>
                <div class="footer-links">
                    <h3>产品服务</h3>
                    <ul>
                        <li><a href="#">跨境支付解决方案</a></li>
                        <li><a href="#">多币种收付款</a></li>
                        <li><a href="#">合规风控服务</a></li>
                        <li><a href="#">API文档</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>关于我们</h3>
                    <ul>
                        <li><a href="#">公司简介</a></li>
                        <li><a href="#">团队介绍</a></li>
                        <li><a href="#">加入我们</a></li>
                        <li><a href="#">联系我们</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>帮助中心</h3>
                    <ul>
                        <li><a href="#">常见问题</a></li>
                        <li><a href="#">开发文档</a></li>
                        <li><a href="#">安全合规</a></li>
                        <li><a href="#">隐私政策</a></li>
                    </ul>
                </div>
            </div>
            <div class="copyright">
                <p>© 2025 EX跨境云支付平台. 保留所有权利.</p>
                <p>沪ICP备2024068336号-1 | 沪公网安备31011002006542号</p>
            </div>
        </div>
    </footer>

    <!-- 页面脚本 -->
    <script>
        // 初始化粒子效果
        document.addEventListener('DOMContentLoaded', function() {
            particlesJS("particles-js", {
                "particles": {
                    "number": {
                        "value": 80,
                        "density": {
                            "enable": true,
                            "value_area": 800
                        }
                    },
                    "color": {
                        "value": "#00e5ff"
                    },
                    "shape": {
                        "type": "circle",
                        "stroke": {
                            "width": 0,
                            "color": "#000000"
                        },
                        "polygon": {
                            "nb_sides": 5
                        }
                    },
                    "opacity": {
                        "value": 0.3,
                        "random": true,
                        "anim": {
                            "enable": false,
                            "speed": 1,
                            "opacity_min": 0.1,
                            "sync": false
                        }
                    },
                    "size": {
                        "value": 3,
                        "random": true,
                        "anim": {
                            "enable": false,
                            "speed": 40,
                            "size_min": 0.1,
                            "sync": false
                        }
                    },
                    "line_linked": {
                        "enable": true,
                        "distance": 150,
                        "color": "#2565f7",
                        "opacity": 0.2,
                        "width": 1
                    },
                    "move": {
                        "enable": true,
                        "speed": 2,
                        "direction": "none",
                        "random": false,
                        "straight": false,
                        "out_mode": "out",
                        "bounce": false,
                        "attract": {
                            "enable": false,
                            "rotateX": 600,
                            "rotateY": 1200
                        }
                    }
                },
                "interactivity": {
                    "detect_on": "canvas",
                    "events": {
                        "onhover": {
                            "enable": true,
                            "mode": "grab"
                        },
                        "onclick": {
                            "enable": true,
                            "mode": "push"
                        },
                        "resize": true
                    },
                    "modes": {
                        "grab": {
                            "distance": 140,
                            "line_linked": {
                                "opacity": 0.6
                            }
                        },
                        "bubble": {
                            "distance": 400,
                            "size": 40,
                            "duration": 2,
                            "opacity": 8,
                            "speed": 3
                        },
                        "repulse": {
                            "distance": 200,
                            "duration": 0.4
                        },
                        "push": {
                            "particles_nb": 4
                        },
                        "remove": {
                            "particles_nb": 2
                        }
                    }
                },
                "retina_detect": true
            });
        });
    </script>
</body>
</html>
