<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1000" height="1500" viewBox="0 0 1000 1500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#ffffff"/>
      <stop offset="100%" stop-color="#00e5ff"/>
    </linearGradient>
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#2565f7"/>
      <stop offset="100%" stop-color="#5d38eb"/>
    </linearGradient>
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="4"/>
      <feOffset dx="2" dy="4" result="offsetblur"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.2"/>
      </feComponentTransfer>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <style type="text/css">
      @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&amp;display=swap');
      
      .title {
        font-family: 'Poppins', sans-serif;
        font-weight: 700;
        fill: url(#titleGradient);
        text-anchor: middle;
      }
      
      .subtitle {
        font-family: 'Poppins', sans-serif;
        font-weight: 400;
        fill: rgba(255, 255, 255, 0.7);
        text-anchor: middle;
      }
      
      .layer-title {
        font-family: 'Poppins', sans-serif;
        font-weight: 600;
        fill: white;
        text-anchor: middle;
      }
      
      .layer-subtitle {
        font-family: 'Poppins', sans-serif;
        font-weight: 400;
        fill: rgba(255, 255, 255, 0.7);
        text-anchor: middle;
        font-size: 14px;
      }
      
      .entity-title {
        font-family: 'Poppins', sans-serif;
        font-weight: 600;
        fill: white;
        text-anchor: middle;
      }
      
      .entity-desc {
        font-family: 'Poppins', sans-serif;
        font-weight: 400;
        fill: rgba(255, 255, 255, 0.7);
        text-anchor: middle;
        font-size: 12px;
      }
      
      .service-title {
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        fill: white;
        text-anchor: middle;
      }
      
      .service-desc {
        font-family: 'Poppins', sans-serif;
        font-weight: 400;
        fill: rgba(255, 255, 255, 0.7);
        text-anchor: middle;
        font-size: 11px;
      }
      
      .footer-note {
        font-family: 'Poppins', sans-serif;
        font-weight: 400;
        fill: rgba(255, 255, 255, 0.5);
        text-anchor: middle;
        font-size: 14px;
      }
      
      .platform-logo {
        font-family: 'Poppins', sans-serif;
        font-weight: 600;
        fill: white;
        text-anchor: middle;
      }
      
      .platform-logo-accent {
        fill: #00e5ff;
      }
      
      .entity-icon-text {
        font-family: sans-serif;
        font-size: 18px;
        fill: white;
        text-anchor: middle;
        dominant-baseline: central;
      }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1000" height="1500" fill="#040d21"/>
  <rect width="1000" height="1500" fill="url(#primaryGradient)" opacity="0.05"/>
  
  <!-- 标题 -->
  <g transform="translate(500, 80)">
    <text class="title" font-size="36">EX跨境云支付平台 - 业务架构图</text>
    <text class="subtitle" font-size="18" y="40">革新全球支付 · 连接无界商机</text>
  </g>
  
  <!-- 上层 -->
  <g transform="translate(500, 200)">
    <text class="layer-title" font-size="20">上层 - 客户与合作伙伴</text>
    
    <!-- 上层容器 -->
    <rect x="-450" y="30" width="900" height="180" rx="15" ry="15" 
          fill="rgba(33, 150, 243, 0.05)" stroke="rgba(33, 150, 243, 0.1)" stroke-width="1"/>
    
    <!-- 上层实体 -->
    <g transform="translate(-340, 110)">
      <rect x="-80" y="-60" width="160" height="120" rx="12" ry="12" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <circle cx="0" cy="-30" r="20" fill="#2196f3"/>
      <text class="entity-icon-text" x="0" y="-30">🏢</text>
      <text class="entity-title" font-size="16" y="0">跨境企业</text>
      <text class="entity-desc" y="20">全球贸易与服务企业</text>
    </g>
    
    <g transform="translate(-110, 110)">
      <rect x="-80" y="-60" width="160" height="120" rx="12" ry="12" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <circle cx="0" cy="-30" r="20" fill="#4caf50"/>
      <text class="entity-icon-text" x="0" y="-30">💻</text>
      <text class="entity-title" font-size="16" y="0">传统支付服务商</text>
      <text class="entity-desc" y="20">全球PSP与支付机构</text>
    </g>
    
    <g transform="translate(120, 110)">
      <rect x="-80" y="-60" width="160" height="120" rx="12" ry="12" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <circle cx="0" cy="-30" r="20" fill="#9c27b0"/>
      <text class="entity-icon-text" x="0" y="-30">⛓️</text>
      <text class="entity-title" font-size="16" y="0">数字资产支付服务商</text>
      <text class="entity-desc" y="20">区块链支付与清算服务</text>
    </g>
    
    <g transform="translate(350, 110)">
      <rect x="-80" y="-60" width="160" height="120" rx="12" ry="12" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <circle cx="0" cy="-30" r="20" fill="#ff9800"/>
      <text class="entity-icon-text" x="0" y="-30">🏦</text>
      <text class="entity-title" font-size="16" y="0">银行</text>
      <text class="entity-desc" y="20">传统金融机构</text>
    </g>
    
    <!-- 连接线 -->
    <line x1="0" y1="210" x2="0" y2="240" stroke="#00e5ff" stroke-width="2"/>
    <circle cx="0" cy="210" r="5" fill="#00e5ff"/>
    <circle cx="0" cy="240" r="5" fill="#00e5ff"/>
  </g>
  
  <!-- 中层 -->
  <g transform="translate(500, 500)">
    <!-- 平台标志 -->
    <rect x="-80" y="-25" width="160" height="40" rx="20" ry="20" 
          fill="url(#primaryGradient)" stroke="rgba(255, 255, 255, 0.2)" stroke-width="1"/>
    <text class="platform-logo" x="0" y="5" font-size="16">EX<tspan class="platform-logo-accent">Cloud</tspan>Pay</text>
    
    <text class="layer-title" font-size="20" y="40">中层 - EX跨境云支付平台</text>
    <text class="layer-subtitle" y="65">全球支付与资金管理一站式解决方案</text>
    
    <!-- 中层容器 -->
    <rect x="-450" y="80" width="900" height="320" rx="15" ry="15" 
          fill="rgba(37, 101, 247, 0.08)" stroke="rgba(37, 101, 247, 0.15)" stroke-width="1"/>
    
    <!-- 中层实体 -->
    <g transform="translate(-250, 150)">
      <rect x="-80" y="-60" width="160" height="120" rx="12" ry="12" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <circle cx="0" cy="-30" r="20" fill="#3f51b5"/>
      <text class="entity-icon-text" x="0" y="-30">💱</text>
      <text class="entity-title" font-size="16" y="0">多币种收付款</text>
      <text class="entity-desc" y="20">法定货币与数字资产支付网络</text>
    </g>
    
    <g transform="translate(0, 150)">
      <rect x="-80" y="-60" width="160" height="120" rx="12" ry="12" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <circle cx="0" cy="-30" r="20" fill="#00bcd4"/>
      <text class="entity-icon-text" x="0" y="-30">💳</text>
      <text class="entity-title" font-size="16" y="0">虚拟卡服务</text>
      <text class="entity-desc" y="20">U卡与全球支付卡发行管理</text>
    </g>
    
    <g transform="translate(250, 150)">
      <rect x="-80" y="-60" width="160" height="120" rx="12" ry="12" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <circle cx="0" cy="-30" r="20" fill="#9c27b0"/>
      <text class="entity-icon-text" x="0" y="-30">🌐</text>
      <text class="entity-title" font-size="16" y="0">全球收单</text>
      <text class="entity-desc" y="20">传统与数字资产收银解决方案</text>
    </g>
    
    <!-- 中层服务 -->
    <g transform="translate(-300, 280)">
      <rect x="-80" y="-30" width="160" height="60" rx="10" ry="10" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <text class="service-title" font-size="14" y="-10">统一账户管理</text>
      <text class="service-desc" y="10">多币种账户与资产集中管理</text>
    </g>
    
    <g transform="translate(-100, 280)">
      <rect x="-80" y="-30" width="160" height="60" rx="10" ry="10" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <text class="service-title" font-size="14" y="-10">智能清结算</text>
      <text class="service-desc" y="10">跨境资金与数字资产清算</text>
    </g>
    
    <g transform="translate(100, 280)">
      <rect x="-80" y="-30" width="160" height="60" rx="10" ry="10" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <text class="service-title" font-size="14" y="-10">实时汇率管理</text>
      <text class="service-desc" y="10">多币种汇率优化与风险管理</text>
    </g>
    
    <g transform="translate(300, 280)">
      <rect x="-80" y="-30" width="160" height="60" rx="10" ry="10" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <text class="service-title" font-size="14" y="-10">全球合规体系</text>
      <text class="service-desc" y="10">跨境支付与资产合规解决方案</text>
    </g>
    
    <g transform="translate(-200, 350)">
      <rect x="-80" y="-30" width="160" height="60" rx="10" ry="10" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <text class="service-title" font-size="14" y="-10">交易监控分析</text>
      <text class="service-desc" y="10">全渠道交易监控与数据分析</text>
    </g>
    
    <g transform="translate(200, 350)">
      <rect x="-80" y="-30" width="160" height="60" rx="10" ry="10" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <text class="service-title" font-size="14" y="-10">开放平台生态</text>
      <text class="service-desc" y="10">开放式API与应用生态系统</text>
    </g>
    
    <!-- 连接线 -->
    <line x1="0" y1="400" x2="0" y2="430" stroke="#00e5ff" stroke-width="2"/>
    <circle cx="0" cy="400" r="5" fill="#00e5ff"/>
    <circle cx="0" cy="430" r="5" fill="#00e5ff"/>
  </g>
  
  <!-- 下层 -->
  <g transform="translate(500, 980)">
    <text class="layer-title" font-size="20">下层 - 金融与服务网络</text>
    
    <!-- 下层容器 -->
    <rect x="-450" y="30" width="900" height="180" rx="15" ry="15" 
          fill="rgba(76, 175, 80, 0.05)" stroke="rgba(76, 175, 80, 0.1)" stroke-width="1"/>
    
    <!-- 下层实体 -->
    <g transform="translate(-340, 110)">
      <rect x="-80" y="-60" width="160" height="120" rx="12" ry="12" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <circle cx="0" cy="-30" r="20" fill="#ff9800"/>
      <text class="entity-icon-text" x="0" y="-30">🏦</text>
      <text class="entity-title" font-size="16" y="0">银行</text>
      <text class="entity-desc" y="20">全球主要银行网络</text>
    </g>
    
    <g transform="translate(-110, 110)">
      <rect x="-80" y="-60" width="160" height="120" rx="12" ry="12" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <circle cx="0" cy="-30" r="20" fill="#4caf50"/>
      <text class="entity-icon-text" x="0" y="-30">💳</text>
      <text class="entity-title" font-size="16" y="0">全球支付服务商</text>
      <text class="entity-desc" y="20">国际知名PSP与收单机构</text>
    </g>
    
    <g transform="translate(120, 110)">
      <rect x="-80" y="-60" width="160" height="120" rx="12" ry="12" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <circle cx="0" cy="-30" r="20" fill="#9c27b0"/>
      <text class="entity-icon-text" x="0" y="-30">⛓️</text>
      <text class="entity-title" font-size="16" y="0">数字资产服务商</text>
      <text class="entity-desc" y="20">数字资产交易与托管机构</text>
    </g>
    
    <g transform="translate(350, 110)">
      <rect x="-80" y="-60" width="160" height="120" rx="12" ry="12" 
            fill="rgba(255, 255, 255, 0.08)" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1"/>
      <circle cx="0" cy="-30" r="20" fill="#f44336"/>
      <text class="entity-icon-text" x="0" y="-30">🔒</text>
      <text class="entity-title" font-size="16" y="0">风控合规服务商</text>
      <text class="entity-desc" y="20">全球合规与风险管理服务</text>
    </g>
  </g>
  
  <!-- 底部说明 -->
  <g transform="translate(500, 1250)">
    <text class="footer-note" x="0" y="0" width="800">
      <tspan x="0" dy="0">EX跨境云支付平台通过创新的技术架构，连接上层客户与下层金融网络，提供安全、高效、灵活的全球资金流转与管理解决方案。</tspan>
      <tspan x="0" dy="20">平台整合了多币种收付款、虚拟卡服务和全球收单等先进功能，支持法定货币与数字资产的全渠道处理，</tspan>
      <tspan x="0" dy="20">满足跨境企业、传统支付服务商、数字资产支付服务商和银行等客户的多样化需求。</tspan>
    </text>
  </g>
</svg>
