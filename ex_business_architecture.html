<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EurewaX跨境云支付平台 - 业务架构图</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        @media print {
            body {
                background-color: white !important;
                background-image: none !important;
                padding: 0 !important;
                margin: 0 !important;
            }
            
            .container {
                box-shadow: none !important;
                border: none !important;
                background: white !important;
                padding: 20px !important;
            }
            
            .title h1 {
                color: #2565f7 !important;
                -webkit-text-fill-color: #2565f7 !important;
            }
            
            .title p, .layer-title, .entity-title, .entity-desc, .service-title, .service-desc, .footer-note {
                color: #333 !important;
            }
            
            .print-button {
                display: none !important;
            }
            
            .layer-container {
                background: rgba(245, 245, 245, 0.5) !important;
                border: 1px solid #eee !important;
            }
            
            .entity, .service {
                background: white !important;
                border: 1px solid #ddd !important;
            }
            
            .connector {
                background: #2565f7 !important;
            }
            
            .connector::before, .connector::after {
                background: #2565f7 !important;
                box-shadow: none !important;
            }
            
            .entity-icon {
                print-color-adjust: exact !important;
                -webkit-print-color-adjust: exact !important;
            }
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .print-button:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }
        
        :root {
            --primary-color: #2565f7;
            --primary-gradient: linear-gradient(135deg, #2565f7, #5d38eb);
            --secondary-color: #0b31a0;
            --dark-color: #051552;
            --accent-color: #00e5ff;
            --light-color: #e6f7ff;
            --text-color: #333333;
            --light-text: #ffffff;
            --bg-color: #040d21;
            --card-bg: rgba(255, 255, 255, 0.95);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --box-shadow: 0 8px 32px 0 rgba(0, 0, 20, 0.1);
            
            /* 新增颜色 */
            --web2-color: #4caf50;
            --web3-color: #9c27b0;
            --bank-color: #ff9800;
            --enterprise-color: #2196f3;
            --risk-color: #f44336;
            --ucard-color: #00bcd4;
            --crypto-color: #673ab7;
            --payment-color: #3f51b5;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            color: var(--text-color);
            background-color: var(--bg-color);
            line-height: 1.6;
            overflow-x: hidden;
            background-image: radial-gradient(circle at 50% 50%, rgba(37, 101, 247, 0.1) 0%, rgba(5, 21, 82, 0.1) 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px;
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .title {
            text-align: center;
            margin-bottom: 40px;
            color: var(--light-text);
        }
        
        .title h1 {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(to right, var(--light-text), var(--accent-color));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            display: inline-block;
        }
        
        .title p {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .architecture-diagram {
            width: 100%;
            position: relative;
        }
        
        /* 架构图样式 */
        .layer {
            margin-bottom: 30px;
            position: relative;
        }
        
        .layer-title {
            text-align: center;
            padding: 10px;
            color: var(--light-text);
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
            position: relative;
            z-index: 2;
        }
        
        .layer-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: var(--accent-color);
            border-radius: 3px;
        }
        
        .layer-container {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.08);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .layer-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(37, 101, 247, 0.05), rgba(93, 56, 235, 0.05));
            z-index: -1;
        }
        
        .upper-layer .layer-container {
            background: rgba(33, 150, 243, 0.05);
            border-color: rgba(33, 150, 243, 0.1);
        }
        
        .middle-layer .layer-container {
            background: rgba(37, 101, 247, 0.08);
            border-color: rgba(37, 101, 247, 0.15);
        }
        
        .lower-layer .layer-container {
            background: rgba(76, 175, 80, 0.05);
            border-color: rgba(76, 175, 80, 0.1);
        }
        
        .entities {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
        }
        
        .entity {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            padding: 15px;
            min-width: 200px;
            flex: 1;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100px;
        }
        
        .entity:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }
        
        .entity-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--light-text);
            margin-bottom: 8px;
        }
        
        .entity-desc {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .entity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 18px;
            color: var(--light-text);
        }
        
        /* 实体颜色 */
        .enterprise .entity-icon {
            background: var(--enterprise-color);
        }
        
        .web2 .entity-icon {
            background: var(--web2-color);
        }
        
        .web3 .entity-icon {
            background: var(--web3-color);
        }
        
        .bank .entity-icon {
            background: var(--bank-color);
        }
        
        .risk .entity-icon {
            background: var(--risk-color);
        }
        
        .payment .entity-icon {
            background: var(--payment-color);
        }
        
        .crypto .entity-icon {
            background: var(--crypto-color);
        }
        
        .ucard .entity-icon {
            background: var(--ucard-color);
        }
        
        /* 连接线 */
        .connector {
            position: absolute;
            left: 50%;
            bottom: -30px;
            transform: translateX(-50%);
            width: 2px;
            height: 30px;
            background: var(--accent-color);
            z-index: 1;
        }
        
        .connector::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: var(--accent-color);
            box-shadow: 0 0 10px var(--accent-color);
        }
        
        .connector::after {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: var(--accent-color);
            box-shadow: 0 0 10px var(--accent-color);
        }
        
        /* 中间层特殊样式 */
        .middle-services {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 20px;
        }
        
        .service {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            padding: 12px;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .service:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            background: rgba(255, 255, 255, 0.12);
        }
        
        .service-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--light-text);
            margin-bottom: 5px;
        }
        
        .service-desc {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        /* 响应式设计 */
        @media (max-width: 992px) {
            .middle-services {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .entities {
                flex-direction: column;
            }
            
            .entity {
                width: 100%;
            }
            
            .middle-services {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 20px;
            }
            
            .title h1 {
                font-size: 28px;
            }
            
            .title p {
                font-size: 16px;
            }
        }
        
        /* 动画效果 */
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(0, 229, 255, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(0, 229, 255, 0); }
            100% { box-shadow: 0 0 0 0 rgba(0, 229, 255, 0); }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        /* 平台标志 */
        .platform-logo {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary-gradient);
            color: var(--light-text);
            padding: 8px 25px;
            border-radius: 30px;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 5px 15px rgba(37, 101, 247, 0.3);
            z-index: 10;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .platform-logo span {
            color: var(--accent-color);
        }
        
        /* 底部说明 */
        .footer-note {
            margin-top: 40px;
            text-align: center;
            color: rgba(255, 255, 255, 0.5);
            font-size: 14px;
            max-width: 800px;
        }
    </style>
</head>
<body>
    <button class="print-button" onclick="window.print()">导出PDF</button>
    <div class="container">
        <div class="title">
            <h1>EurewaX跨境云支付平台 - 业务架构图</h1>
            <p>革新全球支付 · 连接无界商机</p>
        </div>
        
        <div class="architecture-diagram">
            <!-- 上层 -->
            <div class="layer upper-layer">
                <div class="layer-title">上层 - 客户与合作伙伴</div>
                <div class="layer-container">
                    <div class="entities">
                        <div class="entity enterprise">
                            <div class="entity-icon"><i class="fas">🏢</i></div>
                            <div class="entity-title">跨境企业</div>
                            <div class="entity-desc">全球贸易与服务企业</div>
                        </div>
                        
                        <div class="entity web2">
                            <div class="entity-icon"><i class="fas">💻</i></div>
                            <div class="entity-title">传统支付服务商</div>
                            <div class="entity-desc">全球PSP与支付机构</div>
                        </div>
                        
                        <div class="entity web3">
                            <div class="entity-icon"><i class="fas">⛓️</i></div>
                            <div class="entity-title">数字资产支付服务商</div>
                            <div class="entity-desc">区块链支付与清算服务</div>
                        </div>
                        
                        <div class="entity bank">
                            <div class="entity-icon"><i class="fas">🏦</i></div>
                            <div class="entity-title">银行</div>
                            <div class="entity-desc">传统金融机构</div>
                        </div>
                    </div>
                </div>
                <div class="connector"></div>
            </div>
            
            <!-- 中层 -->
            <div class="layer middle-layer">
                <div class="platform-logo">EurewaX<span>Cloud</span>Pay</div>
                <div class="layer-title">中层 - EurewaX跨境云支付平台</div>
                <div style="text-align: center; margin-bottom: 15px; color: rgba(255, 255, 255, 0.7); font-size: 14px;">全球支付与资金管理一站式解决方案</div>
                <div class="layer-container">
                    <div class="entities">
                        <div class="entity payment">
                            <div class="entity-icon"><i class="fas">💱</i></div>
                            <div class="entity-title">多币种收付款</div>
                            <div class="entity-desc">法定货币与数字资产支付网络</div>
                        </div>
                        
                        <div class="entity ucard">
                            <div class="entity-icon"><i class="fas">💳</i></div>
                            <div class="entity-title">虚拟卡服务</div>
                            <div class="entity-desc">U卡与全球支付卡发行管理</div>
                        </div>
                        
                        <div class="entity web3">
                            <div class="entity-icon"><i class="fas">🌐</i></div>
                            <div class="entity-title">全球收单</div>
                            <div class="entity-desc">传统与数字资产收银解决方案</div>
                        </div>
                    </div>
                    
                    <div class="middle-services">
                        <div class="service">
                            <div class="service-title">统一账户管理</div>
                            <div class="service-desc">多币种账户与资产集中管理</div>
                        </div>
                        
                        <div class="service">
                            <div class="service-title">智能清结算</div>
                            <div class="service-desc">跨境资金与数字资产清算</div>
                        </div>
                        
                        <div class="service">
                            <div class="service-title">实时汇率管理</div>
                            <div class="service-desc">多币种汇率优化与风险管理</div>
                        </div>
                        
                        <div class="service">
                            <div class="service-title">全球合规体系</div>
                            <div class="service-desc">跨境支付与资产合规解决方案</div>
                        </div>
                        
                        <div class="service">
                            <div class="service-title">交易监控分析</div>
                            <div class="service-desc">全渠道交易监控与数据分析</div>
                        </div>
                        
                        <div class="service">
                            <div class="service-title">开放平台生态</div>
                            <div class="service-desc">开放式API与应用生态系统</div>
                        </div>
                    </div>
                </div>
                <div class="connector"></div>
            </div>
            
            <!-- 下层 -->
            <div class="layer lower-layer">
                <div class="layer-title">下层 - 金融与服务网络</div>
                <div class="layer-container">
                    <div class="entities">
                        <div class="entity bank">
                            <div class="entity-icon"><i class="fas">🏦</i></div>
                            <div class="entity-title">银行</div>
                            <div class="entity-desc">全球主要银行网络</div>
                        </div>
                        
                        <div class="entity web2">
                            <div class="entity-icon"><i class="fas">💳</i></div>
                            <div class="entity-title">全球支付服务商</div>
                            <div class="entity-desc">国际知名PSP与收单机构</div>
                        </div>
                        
                        <div class="entity web3">
                            <div class="entity-icon"><i class="fas">⛓️</i></div>
                            <div class="entity-title">数字资产服务商</div>
                            <div class="entity-desc">数字资产交易与托管机构</div>
                        </div>
                        
                        <div class="entity risk">
                            <div class="entity-icon"><i class="fas">🔒</i></div>
                            <div class="entity-title">风控合规服务商</div>
                            <div class="entity-desc">全球合规与风险管理服务</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer-note">
            EurewaX跨境云支付平台通过创新的技术架构，连接上层客户与下层金融网络，提供安全、高效、灵活的全球资金流转与管理解决方案。
            平台整合了多币种收付款、虚拟卡服务和全球收单等先进功能，支持法定货币与数字资产的全渠道处理，满足跨境企业、传统支付服务商、数字资产支付服务商和银行等客户的多样化需求。
        </div>
    </div>
    
    <script>
        // 添加图标库
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css';
        document.head.appendChild(link);
        
        // 自动调整页面大小以适应打印
        window.onbeforeprint = function() {
            document.body.classList.add('printing');
        };
        
        window.onafterprint = function() {
            document.body.classList.remove('printing');
        };
    </script>
</body>
</html>
