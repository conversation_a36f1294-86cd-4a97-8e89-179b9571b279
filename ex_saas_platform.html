<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EX SaaS平台管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            display: flex;
            min-height: 100vh;
            background-color: #f5f7fa;
        }
        
        .sidebar {
            width: 220px;
            background-color: #001529;
            color: #fff;
            transition: all 0.3s;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .logo {
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-bottom: 1px solid #002140;
        }
        
        .logo img {
            height: 32px;
        }
        
        .menu {
            padding: 16px 0;
        }
        
        .menu-item {
            padding: 12px 24px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .menu-item:hover {
            background-color: #1890ff;
        }
        
        .menu-item.active {
            background-color: #1890ff;
        }
        
        .menu-item-content {
            display: flex;
            align-items: center;
        }
        
        .menu-item i {
            margin-right: 10px;
            font-size: 16px;
        }
        
        .arrow {
            transition: transform 0.3s;
        }
        
        .arrow.open {
            transform: rotate(90deg);
        }
        
        .submenu {
            background-color: #000c17;
            overflow: hidden;
            max-height: 0;
            transition: max-height 0.3s ease-in-out;
        }
        
        .submenu.open {
            max-height: 300px;
        }
        
        .submenu-item {
            padding: 10px 24px 10px 48px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .submenu-item:hover {
            background-color: #1890ff;
        }
        
        .submenu-item.active {
            background-color: #1890ff;
        }
        
        .content {
            flex: 1;
            padding: 24px;
            margin-left: 220px; /* 与侧边栏宽度相同 */
            width: calc(100% - 220px);
        }
        
        .header {
            height: 64px;
            background-color: #fff;
            box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
            padding: 0 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: 500;
            color: #001529;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #1890ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            margin-right: 8px;
        }
        
        .main-content {
            margin-top: 24px;
            background-color: #fff;
            border-radius: 4px;
            padding: 24px;
            min-height: calc(100vh - 112px);
        }
        
        .breadcrumb {
            margin-bottom: 16px;
            color: #666;
        }
        
        .breadcrumb span {
            margin: 0 8px;
            color: #999;
        }
        
        .product-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .product-table th,
        .product-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .product-table th {
            background-color: #fafafa;
            font-weight: 500;
        }
        
        .product-table tr:hover td {
            background-color: #f5f7fa;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            outline: none;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: #fff;
        }
        
        .btn-primary:hover {
            background-color: #40a9ff;
        }
        
        .btn-view {
            color: #1890ff;
            background: none;
            padding: 4px 8px;
        }
        
        .btn-view:hover {
            background-color: #e6f7ff;
        }
        
        .action-bar {
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .search-box {
            display: flex;
            align-items: center;
        }
        
        .search-input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            width: 240px;
            transition: all 0.3s;
        }
        
        .search-input:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .status {
            padding: 4px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        
        .status-active {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        
        .status-inactive {
            background-color: #fff1f0;
            color: #ff4d4f;
        }
        
        .status-pending {
            background-color: #fff7e6;
            color: #fa8c16;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="logo">
            <img src="https://placeholder.pics/svg/120x40/FFFFFF/1890FF/EX%20SaaS" alt="EX SaaS Logo">
        </div>
        <div class="menu">
            <div class="menu-item active" id="product-menu">
                <div class="menu-item-content">
                    <i>📦</i>
                    <span>产品管理</span>
                </div>
                <div class="arrow open">▶</div>
            </div>
            <div class="submenu open" id="product-submenu">
                <div class="submenu-item active">收款产品</div>
                <div class="submenu-item">结汇产品</div>
                <div class="submenu-item">本地付款产品</div>
                <div class="submenu-item">全球付款产品</div>
                <div class="submenu-item">即期换汇产品</div>
            </div>
            <div class="menu-item" id="tenant-menu">
                <div class="menu-item-content">
                    <i>👥</i>
                    <span>租户管理</span>
                </div>
                <div class="arrow">▶</div>
            </div>
            <div class="submenu" id="tenant-submenu">
                <div class="submenu-item">租户配置</div>
                <div class="submenu-item">租户签约</div>
            </div>
            <div class="menu-item">
                <div class="menu-item-content">
                    <i>🔄</i>
                    <span>代理分销</span>
                </div>
                <div class="arrow">▶</div>
            </div>
            <div class="menu-item">
                <div class="menu-item-content">
                    <i>👤</i>
                    <span>自营会员管理</span>
                </div>
                <div class="arrow">▶</div>
            </div>
        </div>
    </div>
    
    <!-- 页面内容区域 -->
    <div class="content" id="tenant-management-page" style="display: none;">
        <div class="header">
            <div class="page-title">租户管理</div>
            <div class="user-info">
                <div class="user-avatar">A</div>
                <div>管理员</div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="breadcrumb">
                租户管理 <span>></span> 租户配置
            </div>
            <style>
                .filter-container {
                    background-color: #f9f9f9;
                    border-radius: 8px;
                    padding: 16px 20px;
                    margin-bottom: 20px;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
                }
                
                .filter-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 16px;
                }
                
                .filter-title {
                    font-size: 16px;
                    font-weight: 500;
                    color: #333;
                }
                
                .action-buttons {
                    display: flex;
                    gap: 12px;
                }
                
                .action-btn-primary {
                    background-color: #1890ff;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    transition: all 0.3s;
                }
                
                .action-btn-primary:hover {
                    background-color: #40a9ff;
                }
                
                .filter-form {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 16px;
                    align-items: end;
                }
                
                .filter-item {
                    display: flex;
                    flex-direction: column;
                    gap: 6px;
                }
                
                .filter-label {
                    font-size: 13px;
                    color: #666;
                }
                
                .filter-input {
                    padding: 8px 12px;
                    border: 1px solid #d9d9d9;
                    border-radius: 4px;
                    width: 100%;
                    transition: all 0.3s;
                }
                
                .filter-input:focus {
                    border-color: #40a9ff;
                    box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
                    outline: none;
                }
                
                .search-btn {
                    background-color: #1890ff;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    height: 38px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 6px;
                    transition: all 0.3s;
                }
                
                .search-btn:hover {
                    background-color: #40a9ff;
                }
                
                .search-icon {
                    font-size: 16px;
                }
            </style>
            
            <div class="filter-container">
                <div class="filter-header">
                    <div class="filter-title">租户管理</div>
                    <div class="action-buttons">
                        <button class="action-btn-primary">
                            <i class="fas fa-file-import" style="font-size: 14px;"></i>
                            批量导入
                        </button>
                        <button class="action-btn-primary">
                            <i class="fas fa-plus" style="font-size: 14px;"></i>
                            新增租户代理商
                        </button>
                    </div>
                </div>
                
                <form class="filter-form">
                    <div class="filter-item">
                        <label class="filter-label">租户昵称</label>
                        <input class="filter-input" type="text" placeholder="请输入租户昵称">
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">租户ID</label>
                        <input class="filter-input" type="text" placeholder="请输入租户ID">
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">品牌名称</label>
                        <input class="filter-input" type="text" placeholder="请输入品牌名称">
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">是否使用自已域名</label>
                        <select class="filter-input">
                            <option>请选择</option>
                            <option>是</option>
                            <option>否</option>
                        </select>
                    </div>
                    <div class="filter-item">
                        <button type="submit" class="search-btn">
                            <span class="search-icon">🔍</span>
                            查询
                        </button>
                    </div>
                </form>
            </div>
            <style>
                .tenant-table th, .tenant-table td {
                    white-space: nowrap;
                }
                .tenant-table .btn {
                    border: 1px solid #1890ff;
                    background: #fff;
                    color: #1890ff;
                    padding: 4px 10px;
                    margin-right: 4px;
                    font-size: 14px;
                }
                .tenant-table .btn:hover {
                    background: #e6f7ff;
                }
            </style>
            <style>
                .compact-table {
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 14px;
                }
                
                .compact-table th, .compact-table td {
                    padding: 8px 12px;
                    text-align: left;
                    border-bottom: 1px solid #e8e8e8;
                    white-space: nowrap;
                }
                
                .compact-table th {
                    background-color: #fafafa;
                    font-weight: 500;
                    color: #333;
                }
                
                .compact-table tr:hover td {
                    background-color: #f5f7fa;
                }
                
                .tenant-info {
                    display: flex;
                    flex-direction: column;
                }
                
                .tenant-name {
                    font-weight: 500;
                    margin-bottom: 3px;
                }
                
                .tenant-id {
                    color: #666;
                    font-size: 12px;
                }
                
                .tenant-domain {
                    color: #1890ff;
                    font-size: 12px;
                    margin-top: 2px;
                }
                
                .channel-info {
                    display: flex;
                    flex-direction: column;
                }
                
                .channel-id {
                    font-weight: 500;
                    color: #333;
                    margin-top: 2px;
                }
                
                .status-tag {
                    display: inline-block;
                    padding: 2px 8px;
                    border-radius: 2px;
                    font-size: 12px;
                }
                
                .status-active {
                    background-color: #f6ffed;
                    border: 1px solid #b7eb8f;
                    color: #52c41a;
                }
                
                .status-inactive {
                    background-color: #fff7e6;
                    border: 1px solid #ffd591;
                    color: #fa8c16;
                }
                
                .action-btn {
                    border: 1px solid #1890ff;
                    background: #fff;
                    color: #1890ff;
                    padding: 3px 8px;
                    margin-right: 4px;
                    font-size: 12px;
                    border-radius: 2px;
                    cursor: pointer;
                }
                
                .action-btn:hover {
                    background: #e6f7ff;
                }
                
                .time-info {
                    font-size: 12px;
                    color: #999;
                }
            </style>
            
            <table class="compact-table">
                <thead>
                    <tr>
                        <th>租户信息</th>
                        <th>品牌信息</th>
                        <th>状态</th>
                        <th>客户经理</th>
                        <th>时间信息</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div class="tenant-info">
                                <div class="tenant-name">非自有品牌和自有域名名002</div>
                                <div class="tenant-id">ID: zidonghua002</div>
                            </div>
                        </td>
                        <td>
                            <div class="brand-info">
                                <div class="brand-name">Eurewax</div>
                                <div class="domain" style="color: #333; margin-top: 4px;">zidonghua002.uateurewax.com</div>
                            </div>
                        </td>
                        <td>
                            <span class="status-tag status-active">已激活</span>
                        </td>
                        <td>
                            <div class="manager-info">
                                <div class="manager-name">张三</div>
                                <div class="manager-id" style="font-size: 12px; color: #666; margin-top: 2px;">ID: ZS001</div>
                            </div>
                        </td>
                        <td>
                            <div class="time-info">
                                <div>创建: 2025-04-10 15:29:20</div>
                                <div>更新: 2025-04-10 15:29:20</div>
                            </div>
                        </td>
                        <td>
                            <button class="action-btn">详情</button>
                            <button class="action-btn">编辑</button>
                            <button class="action-btn">发送短信</button>
                            <button class="action-btn">日志</button>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="tenant-info">
                                <div class="tenant-name">自有品牌和自有域名001</div>
                                <div class="tenant-id">ID: zidonghua001</div>
                            </div>
                        </td>
                        <td>
                            <div class="brand-info">
                                <div class="brand-name">上海润渠</div>
                                <div class="domain" style="color: #52c41a; margin-top: 4px;">www.zidonghua001.com</div>
                            </div>
                        </td>
                        <td>
                            <span class="status-tag status-inactive">未激活</span>
                        </td>
                        <td>
                            <div class="manager-info">
                                <div class="manager-name">李四</div>
                                <div class="manager-id" style="font-size: 12px; color: #666; margin-top: 2px;">ID: LS002</div>
                            </div>
                        </td>
                        <td>
                            <div class="time-info">
                                <div>创建: 2025-04-10 15:29:20</div>
                                <div>更新: 2025-04-10 15:29:20</div>
                            </div>
                        </td>
                        <td>
                            <button class="action-btn">详情</button>
                            <button class="action-btn">编辑</button>
                            <button class="action-btn">发送短信</button>
                            <button class="action-btn">日志</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 收款产品管理页面 -->
    <div class="content" id="product-management-page">
        <div class="header">
            <div class="page-title">收款产品管理</div>
            <div class="user-info">
                <div class="user-avatar">A</div>
                <div>管理员</div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="breadcrumb">
                产品管理 <span>></span> 收款产品
            </div>
            <div class="filter-container">
                <div class="filter-header">
                    <div class="filter-title">产品管理</div>
                    <div class="action-buttons">
                        <button class="action-btn-primary">
                            <i class="fas fa-plus" style="font-size: 14px;"></i>
                            新增收款产品
                        </button>
                    </div>
                </div>
                
                <form class="filter-form">
                    <div class="filter-item">
                        <label class="filter-label">产品名称</label>
                        <input class="filter-input" type="text" placeholder="请输入产品名称">
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">应用场景</label>
                        <select class="filter-input">
                            <option>请选择</option>
                            <option>电商平台/独立站收款</option>
                            <option>外贸收款</option>
                            <option>开发者收款</option>
                            <option>广告收款</option>
                            <option>物流收款</option>
                        </select>
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">状态</label>
                        <select class="filter-input">
                            <option>请选择</option>
                            <option>已生效</option>
                            <option>未生效</option>
                        </select>
                    </div>
                    <div class="filter-item">
                        <button type="submit" class="search-btn">
                            <span class="search-icon">🔍</span>
                            查询
                        </button>
                    </div>
                </form>
            </div>
            <style>
                .scene-tag {
                    display: inline-block;
                    padding: 2px 8px;
                    margin: 2px;
                    border-radius: 12px;
                    font-size: 12px;
                    background-color: #e6f7ff;
                    color: #1890ff;
                }
                .product-table th, .product-table td {
                    white-space: nowrap;
                }
            </style>
            <table class="compact-table">
                <thead>
                    <tr>
                        <th>产品信息</th>
                        <th>应用场景</th>
                        <th>状态</th>
                        <th>时间信息</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div class="tenant-info">
                                <div class="tenant-name">Stripe</div>
                                <div class="tenant-id">国际支付平台</div>
                            </div>
                        </td>
                        <td>
                            <div style="display: flex; flex-wrap: wrap; gap: 4px; max-width: 300px;">
                                <span class="scene-tag">电商平台/独立站</span>
                                <span class="scene-tag">外贸收款</span>
                                <span class="scene-tag">开发者收款</span>
                                <span class="scene-tag">广告收款</span>
                            </div>
                        </td>
                        <td>
                            <span class="status-tag status-active">已生效</span>
                        </td>
                        <td>
                            <div class="time-info">
                                <div>创建: 2025-01-20 10:45:18</div>
                                <div>更新: 2025-04-05 16:22:37</div>
                            </div>
                        </td>
                        <td>
                            <button class="action-btn">查看</button>
                            <button class="action-btn">编辑</button>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="tenant-info">
                                <div class="tenant-name">PayPal</div>
                                <div class="tenant-id">国际支付平台</div>
                            </div>
                        </td>
                        <td>
                            <div style="display: flex; flex-wrap: wrap; gap: 4px; max-width: 300px;">
                                <span class="scene-tag">电商平台/独立站</span>
                                <span class="scene-tag">外贸收款</span>
                                <span class="scene-tag">开发者收款</span>
                                <span class="scene-tag">广告收款</span>
                            </div>
                        </td>
                        <td>
                            <span class="status-tag status-active">已生效</span>
                        </td>
                        <td>
                            <div class="time-info">
                                <div>创建: 2025-02-12 08:55:33</div>
                                <div>更新: 2025-03-28 11:40:15</div>
                            </div>
                        </td>
                        <td>
                            <button class="action-btn">查看</button>
                            <button class="action-btn">编辑</button>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="tenant-info">
                                <div class="tenant-name">Adyen</div>
                                <div class="tenant-id">欧洲支付平台</div>
                            </div>
                        </td>
                        <td>
                            <div style="display: flex; flex-wrap: wrap; gap: 4px; max-width: 300px;">
                                <span class="scene-tag">电商平台/独立站</span>
                                <span class="scene-tag">外贸收款</span>
                                <span class="scene-tag">物流收款</span>
                                <span class="scene-tag">开发者收款</span>
                            </div>
                        </td>
                        <td>
                            <span class="status-tag status-inactive">未生效</span>
                        </td>
                        <td>
                            <div class="time-info">
                                <div>创建: 2025-04-01 15:20:47</div>
                                <div>更新: 2025-04-15 13:05:29</div>
                            </div>
                        </td>
                        <td>
                            <button class="action-btn">查看</button>
                            <button class="action-btn">编辑</button>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="tenant-info">
                                <div class="tenant-name">WorldPay</div>
                                <div class="tenant-id">英国支付平台</div>
                            </div>
                        </td>
                        <td>
                            <div style="display: flex; flex-wrap: wrap; gap: 4px; max-width: 300px;">
                                <span class="scene-tag">电商平台/独立站</span>
                            </div>
                        </td>
                        <td>
                            <span class="status-tag status-inactive">未生效</span>
                        </td>
                        <td>
                            <div class="time-info">
                                <div>创建: 2024-11-25 09:10:52</div>
                                <div>更新: 2025-02-18 14:35:08</div>
                            </div>
                        </td>
                        <td>
                            <button class="action-btn">查看</button>
                            <button class="action-btn">编辑</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 租户签约页面 -->
    <div class="content" id="tenant-contract-page" style="display: none;">
        <div class="header">
            <div class="page-title">租户签约管理</div>
            <div class="user-info">
                <div class="user-avatar">A</div>
                <div>管理员</div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="breadcrumb">
                租户管理 <span>></span> 租户签约
            </div>
            
            <div class="filter-container">
                <div class="filter-header">
                    <div class="filter-title">租户签约管理</div>
                    <div class="action-buttons">
                        <button class="action-btn-primary">
                            <i class="fas fa-plus" style="font-size: 14px;"></i>
                            新增租户签约
                        </button>
                    </div>
                </div>
                
                <form class="filter-form">
                    <div class="filter-item">
                        <label class="filter-label">租户ID</label>
                        <input class="filter-input" type="text" placeholder="请输入租户ID">
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">租户签约名称</label>
                        <input class="filter-input" type="text" placeholder="请输入签约名称">
                    </div>
                    <div class="filter-item">
                        <label class="filter-label">状态</label>
                        <select class="filter-input">
                            <option>请选择</option>
                            <option>未生效</option>
                            <option>生效中</option>
                            <option>已失效</option>
                        </select>
                    </div>
                    <div class="filter-item">
                        <button type="submit" class="search-btn">
                            <span class="search-icon">🔍</span>
                            查询
                        </button>
                    </div>
                </form>
            </div>
            
            <style>
                .contract-status-tag {
                    display: inline-block;
                    padding: 2px 8px;
                    border-radius: 2px;
                    font-size: 12px;
                }
                
                .contract-status-pending {
                    background-color: #fff7e6;
                    border: 1px solid #ffd591;
                    color: #fa8c16;
                }
                
                .contract-status-active {
                    background-color: #f6ffed;
                    border: 1px solid #b7eb8f;
                    color: #52c41a;
                }
                
                .contract-status-expired {
                    background-color: #f5f5f5;
                    border: 1px solid #d9d9d9;
                    color: #999;
                }
                
                .product-tag {
                    display: inline-block;
                    padding: 2px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                    margin-right: 4px;
                    margin-bottom: 4px;
                    background-color: #e6f7ff;
                    color: #1890ff;
                }
            </style>
            
            <table class="compact-table">
                <thead>
                    <tr>
                        <th>租户ID</th>
                        <th>租户签约名称</th>
                        <th>签约产品</th>
                        <th>协议签署日期</th>
                        <th>签约截止日期</th>
                        <th>状态</th>
                        <th>创建/更新时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>zidonghua001</td>
                        <td>上海润渠-标准签约</td>
                        <td>
                            <div style="display: flex; flex-wrap: wrap; gap: 4px;">
                                <span class="product-tag">收款</span>
                                <span class="product-tag">结汇</span>
                            </div>
                        </td>
                        <td>2025-01-15</td>
                        <td>2026-01-14</td>
                        <td><span class="contract-status-tag contract-status-active">生效中</span></td>
                        <td>
                            <div class="time-info">
                                <div>创建: 2025-01-15 10:30:20</div>
                                <div>更新: 2025-01-15 10:30:20</div>
                            </div>
                        </td>
                        <td>
                            <button class="action-btn">详情</button>
                            <button class="action-btn">编辑</button>
                            <button class="action-btn">续签</button>
                        </td>
                    </tr>
                    <tr>
                        <td>zidonghua002</td>
                        <td>Eurewax-全功能签约</td>
                        <td>
                            <div style="display: flex; flex-wrap: wrap; gap: 4px;">
                                <span class="product-tag">收款</span>
                                <span class="product-tag">结汇</span>
                                <span class="product-tag">本地付款</span>
                            </div>
                        </td>
                        <td>2025-02-20</td>
                        <td>2025-08-19</td>
                        <td><span class="contract-status-tag contract-status-active">生效中</span></td>
                        <td>
                            <div class="time-info">
                                <div>创建: 2025-02-20 14:15:30</div>
                                <div>更新: 2025-02-20 14:15:30</div>
                            </div>
                        </td>
                        <td>
                            <button class="action-btn">详情</button>
                            <button class="action-btn">编辑</button>
                            <button class="action-btn">续签</button>
                        </td>
                    </tr>
                    <tr>
                        <td>zidonghua003</td>
                        <td>广州易通-基础签约</td>
                        <td>
                            <div style="display: flex; flex-wrap: wrap; gap: 4px;">
                                <span class="product-tag">收款</span>
                            </div>
                        </td>
                        <td>2024-10-05</td>
                        <td>2025-04-04</td>
                        <td><span class="contract-status-tag contract-status-expired">已失效</span></td>
                        <td>
                            <div class="time-info">
                                <div>创建: 2024-10-05 09:20:15</div>
                                <div>更新: 2025-04-05 00:00:01</div>
                            </div>
                        </td>
                        <td>
                            <button class="action-btn">详情</button>
                            <button class="action-btn">续签</button>
                        </td>
                    </tr>
                    <tr>
                        <td>zidonghua004</td>
                        <td>深圳创联-企业签约</td>
                        <td>
                            <div style="display: flex; flex-wrap: wrap; gap: 4px;">
                                <span class="product-tag">收款</span>
                                <span class="product-tag">本地付款</span>
                            </div>
                        </td>
                        <td>2025-04-15</td>
                        <td>2026-04-14</td>
                        <td><span class="contract-status-tag contract-status-pending">未生效</span></td>
                        <td>
                            <div class="time-info">
                                <div>创建: 2025-04-15 16:45:10</div>
                                <div>更新: 2025-04-15 16:45:10</div>
                            </div>
                        </td>
                        <td>
                            <button class="action-btn">详情</button>
                            <button class="action-btn">编辑</button>
                            <button class="action-btn">激活</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        // 初始化页面显示
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.content').forEach(page => {
                page.style.display = 'none';
            });
            // 显示指定页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.style.display = 'block';
            }
        }
        
        // 初始显示产品管理页面
        showPage('product-management-page');
        
        // 菜单交互
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function() {
                const arrow = this.querySelector('.arrow');
                const submenu = this.nextElementSibling;
                
                if (submenu && submenu.classList.contains('submenu')) {
                    arrow.classList.toggle('open');
                    submenu.classList.toggle('open');
                }
                
                // 激活当前菜单项
                document.querySelectorAll('.menu-item').forEach(menuItem => {
                    menuItem.classList.remove('active');
                });
                this.classList.add('active');
                
                // 切换页面
                const menuText = this.querySelector('span').textContent;
                if (menuText === '租户管理') {
                    showPage('tenant-management-page');
                } else if (menuText === '产品管理') {
                    showPage('product-management-page');
                }
            });
        });
        
        // 子菜单交互
        document.querySelectorAll('.submenu-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.submenu-item').forEach(subItem => {
                    subItem.classList.remove('active');
                });
                this.classList.add('active');
                
                // 切换到相应页面
                const submenuText = this.textContent;
                const parentMenu = this.parentElement.previousElementSibling.querySelector('span').textContent;
                
                if (parentMenu === '产品管理') {
                    showPage('product-management-page');
                    // 更新面包屑
                    const breadcrumb = document.querySelector('#product-management-page .breadcrumb');
                    if (breadcrumb) {
                        breadcrumb.innerHTML = '产品管理 <span>></span> ' + submenuText;
                    }
                } else if (parentMenu === '租户管理') {
                    if (submenuText === '租户配置') {
                        showPage('tenant-management-page');
                        // 更新面包屑
                        const breadcrumb = document.querySelector('#tenant-management-page .breadcrumb');
                        if (breadcrumb) {
                            breadcrumb.innerHTML = '租户管理 <span>></span> ' + submenuText;
                        }
                    } else if (submenuText === '租户签约') {
                        showPage('tenant-contract-page');
                        // 更新面包屑
                        const breadcrumb = document.querySelector('#tenant-contract-page .breadcrumb');
                        if (breadcrumb) {
                            breadcrumb.innerHTML = '租户管理 <span>></span> ' + submenuText;
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
