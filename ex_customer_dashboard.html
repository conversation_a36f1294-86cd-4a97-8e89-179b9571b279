<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EurewaX 客户中心</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }
        
        body {
            background-color: #f8faff;
            color: #333;
            display: flex;
            min-height: 100vh;
            font-size: 14px;
            line-height: 1.5;
            background-image: radial-gradient(circle at 90% 10%, rgba(0, 102, 255, 0.03) 0%, transparent 60%), 
                           radial-gradient(circle at 10% 90%, rgba(0, 102, 255, 0.03) 0%, transparent 60%);
        }
        
        /* 左侧菜单样式 */
        .sidebar {
            width: 240px;
            background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
            padding: 20px 0;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 100;
            border-right: 1px solid rgba(0,0,0,0.03);
        }
        
        .logo {
            padding: 0 20px 20px;
            margin-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        
        .logo img {
            height: 36px;
            margin-right: 10px;
        }
        
        .logo-text {
            font-size: 20px;
            font-weight: 600;
            color: #0066cc;
        }
        
        .menu-item {
            padding: 12px 18px;
            display: flex;
            align-items: center;
            color: #666;
            text-decoration: none;
            transition: all 0.3s ease;
            margin-bottom: 5px;
            border-radius: 0 20px 20px 0;
            margin-right: 15px;
        }
        
        .menu-item i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }
        
        .menu-item.active {
            background: rgba(0, 102, 204, 0.08);
            color: #0066cc;
            border-left: 3px solid #0066cc;
            font-weight: 500;
            box-shadow: 0 2px 5px rgba(0, 102, 204, 0.1);
        }
        
        .menu-item:hover {
            background: rgba(0, 102, 204, 0.05);
        }
        
        .menu-category {
            font-size: 12px;
            color: #999;
            padding: 16px 20px 8px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 240px;
            padding: 25px 35px;
            max-width: 1200px;
        }
        
        /* 顶部导航 */
        .header-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            margin-bottom: 25px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .logo {
            display: flex;
            align-items: center;
        }
        
        .logo img {
            height: 30px;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
        }
        
        .notification-icon {
            position: relative;
            cursor: pointer;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #ff4d4f;
            color: white;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 步骤导航 - 缩小版 */
        .onboarding-steps {
            background: linear-gradient(135deg, #ffffff 0%, #f9fafc 100%);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 25px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.04);
            border: 1px solid rgba(0,0,0,0.02);
        }
        
        .step-container {
            display: flex;
            justify-content: space-between;
            max-height: 70px; /* 缩小高度 */
        }
        
        .step {
            display: flex;
            align-items: center;
            padding: 12px 18px;
            border-radius: 10px;
            transition: all 0.3s ease;
            position: relative;
            background: rgba(255,255,255,0.7);
            border: 1px solid rgba(0,0,0,0.03);
            margin-bottom: 5px;
        }
        
        .step.active {
            background: rgba(0, 102, 204, 0.08);
            border: 1px solid rgba(0, 102, 204, 0.15);
            box-shadow: 0 3px 10px rgba(0, 102, 204, 0.08);
        }
        
        .step-icon {
            width: 42px;
            height: 42px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f5f7fa 0%, #ebeef5 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: #666;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            border: 2px solid white;
        }
        
        .step.active .step-icon {
            background: linear-gradient(135deg, #0066cc 0%, #0052a3 100%);
            color: white;
            box-shadow: 0 3px 8px rgba(0, 102, 204, 0.3);
        }
        
        .step-content h4 {
            font-size: 13px;
            margin: 0;
            white-space: nowrap;
        }
        
        .step-desc {
            font-size: 11px;
            color: #666;
            margin: 0;
        }
        
        .progress-text {
            text-align: center;
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
        
        /* 余额部分 */
        .dashboard-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
            border-radius: 16px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.04);
            overflow: hidden;
            margin-bottom: 25px;
            animation: fadeIn 0.5s ease;
            border: 1px solid rgba(0,102,255,0.05);
            position: relative;
        }
        
        .dashboard-container::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 150px;
            height: 150px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="150" height="150" viewBox="0 0 150 150"><path d="M0,0 L150,0 L150,150 Z" fill="rgba(0,102,255,0.03)"/></svg>');
            pointer-events: none;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 0 16px 0;
            margin-bottom: 25px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .section-header h3 {
            font-size: 20px;
            font-weight: 600;
            color: #222;
            position: relative;
            padding-left: 12px;
        }
        
        .section-header h3:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 18px;
            background: linear-gradient(to bottom, #0066cc, #0099ff);
            border-radius: 2px;
            box-shadow: 0 0 10px rgba(0, 102, 255, 0.3);
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
        }
        
        .toggle-btn, .settings-btn {
            background: linear-gradient(135deg, #f8f9fa 0%, #f0f0f0 100%);
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 8px 14px;
            font-size: 12px;
            cursor: pointer;
            margin-right: 10px;
            color: #555;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        
        .toggle-btn:hover, .settings-btn:hover {
            background: linear-gradient(135deg, #f0f0f0 0%, #e8e8e8 100%);
            box-shadow: 0 2px 5px rgba(0,0,0,0.08);
            transform: translateY(-1px);
        }
        
        .settings-btn {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }
        
        /* CNY换算模块 */
        .conversion-module {
            background: linear-gradient(135deg, #f0f7ff 0%, #e6f0fd 100%);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            text-align: center;
            display: none; /* 默认隐藏 */
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.02);
        }
        
        .total-balance {
            display: flex;
            justify-content: center;
            align-items: baseline;
            margin-bottom: 5px;
        }
        
        .balance-amount {
            font-size: 32px;
            font-weight: 600;
            margin-right: 5px;
        }
        
        .balance-currency select {
            border: none;
            background: transparent;
            font-size: 18px;
            font-weight: 600;
            color: #333;
            cursor: pointer;
        }
        
        .balance-note {
            font-size: 12px;
            color: #888;
            margin-bottom: 15px;
        }
        
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #0066ff 0%, #0052cc 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 22px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 8px rgba(0, 102, 255, 0.2);
        }
        
        .action-btn:hover {
            background: #0052cc;
        }
        
        /* 币种列表 */
        .currency-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
            gap: 15px;
        }
        
        .currency-item {
            display: flex;
            flex-direction: column;
            background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
            border-radius: 16px;
            padding: 18px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 12px rgba(0,0,0,0.04);
            border: 1px solid rgba(0,102,255,0.05);
            position: relative;
            overflow: hidden;
            cursor: grab;
            touch-action: none; /* 修复触摸屏拖拽问题 */
        }
        
        .currency-item.sortable-ghost {
            opacity: 0.4;
            background: #f0f7ff;
        }
        
        .currency-item.sortable-chosen {
            box-shadow: 0 10px 20px rgba(0,102,255,0.1);
            transform: scale(1.02);
        }
        
        .currency-item::after {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 60px;
            height: 60px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60"><path d="M0,60 L60,60 L60,0 Z" fill="rgba(0,102,255,0.02)"/></svg>');
            pointer-events: none;
        }
        
        .currency-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .currency-flag {
            width: 38px;
            height: 38px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 12px;
            background: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 8px rgba(0,0,0,0.08);
            border: 2px solid #ffffff;
            position: relative;
        }
        
        .currency-flag img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .currency-flag::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
            border-radius: 50%;
        }
        
        .currency-code-icon {
            font-size: 16px;
            font-weight: bold;
            color: #0066cc;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            display: none; /* 隐藏币种图标，改用国旗 */
        }
        
        .currency-header {
            display: flex;
            align-items: center;
            width: 100%;
            margin-bottom: 12px;
        }
        
        .currency-code {
            font-weight: 600;
            margin-right: auto;
        }
        
        .currency-amount {
            font-weight: 600;
            font-size: 22px;
            color: #222;
            margin: 5px 0;
            position: relative;
            z-index: 1;
        }
        
        .currency-amount::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 30px;
            height: 2px;
            background: linear-gradient(90deg, rgba(0,102,255,0.5), transparent);
            border-radius: 2px;
        }
        
        /* 币种模块布局 */
        .currency-module {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        /* 币种列表 */
        .currency-list {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 16px;
            margin-right: 20px;
        }
        
        /* 右侧操作区域 */
        .currency-actions {
            width: 180px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #67a3e5 0%, #5590d9 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 14px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 6px rgba(101, 158, 228, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .action-btn i {
            margin-right: 8px;
        }
        
        .action-btn:hover {
            background: linear-gradient(135deg, #5590d9 0%, #4a7fc8 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(101, 158, 228, 0.3);
        }
        
        .global-action-btn i {
            margin-right: 8px;
        }
        
        .global-action-btn:hover {
            background: linear-gradient(135deg, #0052cc 0%, #004099 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 102, 255, 0.25);
        }
        
        /* 模态框 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 10% auto;
            width: 90%;
            max-width: 500px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            animation: modalFadeIn 0.3s;
            overflow: hidden;
        }
        
        @keyframes modalFadeIn {
            from {opacity: 0; transform: translateY(-20px);}
            to {opacity: 1; transform: translateY(0);}
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid #eaeaea;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        
        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .close-modal {
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
            transition: color 0.2s ease;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }
        
        .close-modal:hover {
            color: #333;
            background-color: rgba(0,0,0,0.05);
        }
        
        .modal-body {
            padding: 24px;
        }
        
        .settings-section {
            margin-bottom: 24px;
        }
        
        .settings-section h4 {
            margin-top: 0;
            margin-bottom: 16px;
            font-size: 16px;
            color: #333;
            display: flex;
            align-items: center;
        }
        
        .settings-section h4 i {
            margin-right: 8px;
            color: #0066ff;
        }
        
        /* 币种开关列表 */
        .currency-toggles {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
            gap: 16px;
        }
        
        .currency-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 12px;
            background: #f8f9fa;
            border-radius: 10px;
            transition: all 0.2s ease;
        }
        
        .currency-toggle:hover {
            background: #f0f0f0;
        }
        
        .currency-toggle-label {
            display: flex;
            align-items: center;
            font-weight: 500;
        }
        
        .currency-toggle-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f0f7ff 0%, #e6f0fd 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-weight: bold;
            color: #0066ff;
            font-size: 12px;
        }
        
        /* 开关样式 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .3s;
            border-radius: 20px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .3s;
            border-radius: 50%;
        }
        
        input:checked + .toggle-slider {
            background-color: #0066ff;
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }
        
        /* 下拉选择器 */
        .currency-select-container {
            position: relative;
            margin-top: 10px;
        }
        
        .currency-select {
            width: 100%;
            padding: 12px 16px;
            font-size: 16px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background-color: white;
            appearance: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .currency-select:focus {
            outline: none;
            border-color: #0066ff;
            box-shadow: 0 0 0 2px rgba(0, 102, 255, 0.1);
        }
        
        .currency-select-container:after {
            content: '\f078';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            pointer-events: none;
        }
        
        .currency-option {
            display: flex;
            align-items: center;
        }
        
        .currency-option-icon {
            margin-right: 8px;
            font-weight: bold;
            color: #0066ff;
        }
        
        .modal-footer {
            padding: 20px 24px;
            text-align: right;
            border-top: 1px solid #eaeaea;
            background: #f8f9fa;
        }
        
        .primary-btn {
            background: #0066ff;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .primary-btn:hover {
            background: #0052cc;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .step-container {
                flex-direction: column;
                max-height: none;
            }
            
            .step {
                width: 100%;
                margin-bottom: 10px;
            }
            
            .currency-list {
                grid-template-columns: 1fr;
            }
        }
        
        /* 优化页面设计 */
        .user-details h2 {
            font-size: 20px;
            font-weight: 600;
            color: #222;
            position: relative;
            display: inline-block;
            padding-bottom: 5px;
        }
        
        .user-details h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #0066cc, transparent);
        }
        
        .balance-section {
            padding: 0;
            margin-bottom: 30px;
            position: relative;
        }
        
        .balance-section::after {
            content: '';
            position: absolute;
            top: 20px;
            right: 20px;
            width: 200px;
            height: 200px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><circle cx="100" cy="100" r="80" fill="none" stroke="rgba(0,102,255,0.03)" stroke-width="2"/><circle cx="100" cy="100" r="40" fill="none" stroke="rgba(0,102,255,0.05)" stroke-width="2"/></svg>');
            pointer-events: none;
            opacity: 0.5;
            z-index: 0;
        }
        
        /* 获取币种符号 */
        .currency-code-icon {
            font-size: 16px;
            font-weight: bold;
            color: #0066cc;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            display: none; /* 隐藏币种图标，改用国旗 */
        }
        
        /* 获取国旗代码 */
        .currency-flag {
            width: 38px;
            height: 38px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 12px;
            background: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 8px rgba(0,0,0,0.08);
            border: 2px solid #ffffff;
            position: relative;
        }
        
        .currency-flag img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .currency-flag::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
            border-radius: 50%;
        }
        
        .drag-handle {
            cursor: grab;
            position: absolute;
            top: 10px;
            right: 10px;
            color: #aaa;
            font-size: 16px;
            z-index: 10;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255,255,255,0.8);
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        
        .drag-handle:hover {
            color: #0066cc;
            background: rgba(240,247,255,0.9);
            transform: scale(1.1);
        }
        
        .currency-item:hover .drag-handle {
            opacity: 1;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="navbar">
        <div class="container">
            <div class="logo">
                <img src="https://placeholder.com/wp-content/uploads/2018/10/placeholder.com-logo1.png" alt="EurewaX Logo">
            </div>
            <div class="user-menu">
                <div class="notification-icon">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">2</span>
                </div>
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <span>上海聚通商贸有限公司</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 左侧菜单 -->
    <div class="sidebar">
        <div class="logo">
            <img src="https://via.placeholder.com/36" alt="EurewaX Logo">
            <div class="logo-text">EurewaX</div>
        </div>
        
        <div class="menu-category">主要功能</div>
        <a href="#" class="menu-item active">
            <i class="fas fa-tachometer-alt"></i> 仪表盘
        </a>
        <a href="#" class="menu-item">
            <i class="fas fa-exchange-alt"></i> 交易记录
        </a>
        <a href="#" class="menu-item">
            <i class="fas fa-wallet"></i> 账户管理
        </a>
        <a href="#" class="menu-item">
            <i class="fas fa-chart-line"></i> 数据分析
        </a>
        
        <div class="menu-category">其他服务</div>
        <a href="#" class="menu-item">
            <i class="fas fa-headset"></i> 客户支持
        </a>
        <a href="#" class="menu-item">
            <i class="fas fa-cog"></i> 设置
        </a>
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 步骤导航 - 缩小版 -->
        <div class="onboarding-steps">
            <div class="step-container">
                <div class="step active">
                    <div class="step-icon"><i class="fas fa-user-plus"></i></div>
                    <div class="step-content">
                        <h4>创建账号/登录账号</h4>
                        <p class="step-desc">简单几步完成注册</p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-icon"><i class="fas fa-exchange-alt"></i></div>
                    <div class="step-content">
                        <h4>发送数字与合作方</h4>
                        <p class="step-desc">安全传输，实时处理</p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="step-content">
                        <h4>完成一键入账</h4>
                        <p class="step-desc">快速到账，安全可靠</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 头部信息 -->
        <div class="header-section">
            <div class="user-info">
                <div class="user-details">
                    <h2>您好ABC客户</h2>
                </div>
            </div>
            <div class="header-actions">
                <button class="action-btn" style="background: transparent; color: #666; box-shadow: none;"><i class="fas fa-bell"></i></button>
                <button class="action-btn" style="background: transparent; color: #666; box-shadow: none;"><i class="fas fa-cog"></i></button>
            </div>
        </div>

        <!-- 账户余额部分 -->
        <div class="balance-section">
            <div class="section-header">
                <h3>账户余额</h3>
                <div class="header-actions">
                    <button id="toggleConversion" class="toggle-btn">显示换算币种余额</button>
                    <button id="currencySettings" class="settings-btn"><i class="fas fa-cog"></i></button>
                </div>
            </div>
            
            <!-- CNY换算模块 - 默认隐藏 -->
            <div id="cnyConversionModule" class="conversion-module">
                <div class="total-balance">
                    <div class="balance-amount">0.00</div>
                    <div class="balance-currency">
                        <select id="conversionCurrency" style="border: none; background: transparent; font-size: 18px; font-weight: 600; color: #333; cursor: pointer;">
                            <option value="CNY">¥ CNY</option>
                            <option value="CNH">¥ CNH</option>
                        </select>
                    </div>
                </div>
                <div class="balance-note">折合成人民币总额，仅供参考</div>
            </div>
            
            <!-- 币种模块 -->
            <div class="currency-module">
                <!-- 左侧币种列表 -->
                <div class="currency-list" id="sortable-currencies">
                    <div class="currency-item" data-currency="PHP">
                        <div class="currency-header">
                            <div class="currency-flag">
                                <img src="https://flagcdn.com/w80/ph.png" alt="Philippines flag">
                                <span class="currency-code-icon">₱</span>
                            </div>
                            <div class="currency-code">PHP</div>
                        </div>
                        <div class="currency-amount">0.00</div>
                        <div class="drag-handle">
                            <i class="fas fa-grip-lines"></i>
                        </div>
                    </div>
                    <div class="currency-item" data-currency="NGN">
                        <div class="currency-header">
                            <div class="currency-flag">
                                <img src="https://flagcdn.com/w80/ng.png" alt="Nigeria flag">
                                <span class="currency-code-icon">₦</span>
                            </div>
                            <div class="currency-code">NGN</div>
                        </div>
                        <div class="currency-amount">0.00</div>
                        <div class="drag-handle">
                            <i class="fas fa-grip-lines"></i>
                        </div>
                    </div>
                    <div class="currency-item" data-currency="IDR">
                        <div class="currency-header">
                            <div class="currency-flag">
                                <img src="https://flagcdn.com/w80/id.png" alt="Indonesia flag">
                                <span class="currency-code-icon">Rp</span>
                            </div>
                            <div class="currency-code">IDR</div>
                        </div>
                        <div class="currency-amount">0.00</div>
                        <div class="drag-handle">
                            <i class="fas fa-grip-lines"></i>
                        </div>
                    </div>
                    <div class="currency-item" data-currency="NOK" style="display: flex; flex-direction: column; justify-content: center; background: linear-gradient(135deg, #f0f7ff 0%, #e6f0fd 100%);">
                        <div style="display: flex; flex-direction: column; gap: 10px; padding: 8px 0;">
                            <button class="action-btn" style="padding: 8px; font-size: 12px; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); border: 1px solid rgba(0,102,255,0.2); color: #0066cc; box-shadow: 0 2px 5px rgba(0,102,255,0.1); border-radius: 8px; transition: all 0.2s ease;">
                                <i class="fas fa-paper-plane" style="color: #0066cc; margin-right: 5px;"></i> 付款
                            </button>
                            <button class="action-btn" style="padding: 8px; font-size: 12px; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); border: 1px solid rgba(0,102,255,0.2); color: #0066cc; box-shadow: 0 2px 5px rgba(0,102,255,0.1); border-radius: 8px; transition: all 0.2s ease;">
                                <i class="fas fa-wallet" style="color: #0066cc; margin-right: 5px;"></i> 提现
                            </button>
                            <button class="action-btn" style="padding: 8px; font-size: 12px; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); border: 1px solid rgba(0,102,255,0.2); color: #0066cc; box-shadow: 0 2px 5px rgba(0,102,255,0.1); border-radius: 8px; transition: all 0.2s ease;">
                                <i class="fas fa-exchange-alt" style="color: #0066cc; margin-right: 5px;"></i> 换汇
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 银行账户信息 -->
        <div class="balance-section">
            <div class="section-header">
                <h3>银行账户信息</h3>
            </div>
            <div class="bank-accounts">
                <!-- 这里可以添加银行账户信息 -->
            </div>
        </div>

        <!-- 近期交易记录 -->
        <div class="balance-section">
            <div class="section-header">
                <h3>近期交易</h3>
            </div>
            <div class="transactions">
                <!-- 这里可以添加交易记录 -->
            </div>
        </div>
    </div>

    <!-- 币种设置模态框 -->
    <div id="currencySettingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>币种设置</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h4><i class="fas fa-globe"></i> 显示币种</h4>
                    <div class="currency-toggles">
                        <div class="currency-toggle">
                            <div class="currency-toggle-label">
                                <div class="currency-toggle-icon">₱</div>
                                <span>PHP</span>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" value="PHP" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="currency-toggle">
                            <div class="currency-toggle-label">
                                <div class="currency-toggle-icon">₦</div>
                                <span>NGN</span>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" value="NGN" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="currency-toggle">
                            <div class="currency-toggle-label">
                                <div class="currency-toggle-icon">Rp</div>
                                <span>IDR</span>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" value="IDR" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="currency-toggle">
                            <div class="currency-toggle-label">
                                <div class="currency-toggle-icon">kr</div>
                                <span>NOK</span>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" value="NOK" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="currency-toggle">
                            <div class="currency-toggle-label">
                                <div class="currency-toggle-icon">$</div>
                                <span>USD</span>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" value="USD">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="currency-toggle">
                            <div class="currency-toggle-label">
                                <div class="currency-toggle-icon">€</div>
                                <span>EUR</span>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" value="EUR">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="currency-toggle">
                            <div class="currency-toggle-label">
                                <div class="currency-toggle-icon">£</div>
                                <span>GBP</span>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" value="GBP">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="currency-toggle">
                            <div class="currency-toggle-label">
                                <div class="currency-toggle-icon">¥</div>
                                <span>JPY</span>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" value="JPY">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
                <!-- 移除换算货币选项 -->
            </div>
            <div class="modal-footer">
                <button id="saveSettings" class="primary-btn">保存设置</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化拖拽功能
            setTimeout(function() {
                console.log('初始化拖拽功能...');
                const sortableList = document.getElementById('sortable-currencies');
                if (sortableList) {
                    console.log('找到币种列表元素:', sortableList);
                    const sortableInstance = new Sortable(sortableList, {
                        animation: 150,
                        handle: '.drag-handle',
                        ghostClass: 'sortable-ghost',
                        chosenClass: 'sortable-chosen',
                        filter: '.action-btn, .currency-item[data-currency="NOK"]',
                        preventOnFilter: false,
                        draggable: '.currency-item[data-currency]:not([data-currency="NOK"])',
                        onStart: function(evt) {
                            console.log('开始拖拽:', evt.item.getAttribute('data-currency'));
                        },
                        onEnd: function(evt) {
                            console.log('拖拽结束');
                            // 获取新的币种顺序
                            const newOrder = [];
                            document.querySelectorAll('#sortable-currencies .currency-item[data-currency]').forEach(item => {
                                const currency = item.getAttribute('data-currency');
                                if (currency && currency !== 'NOK') {
                                    newOrder.push(currency);
                                    console.log('添加币种到新顺序:', currency);
                                }
                            });
                            
                            console.log('新的币种顺序:', newOrder);
                            // 将新顺序保存到本地存储
                            localStorage.setItem('currencyOrder', JSON.stringify(newOrder));
                            
                            // 显示拖拽成功提示
                            showToast('币种顺序已更新');
                        }
                    });
                    console.log('拖拽功能初始化完成');
                } else {
                    console.error('找不到币种列表元素!');
                }
            }, 500);
            // 切换CNY换算模块显示/隐藏
            const toggleBtn = document.getElementById('toggleConversion');
            const conversionModule = document.getElementById('cnyConversionModule');
            
            toggleBtn.addEventListener('click', function() {
                if (conversionModule.style.display === 'none' || conversionModule.style.display === '') {
                    conversionModule.style.display = 'block';
                    toggleBtn.textContent = '隐藏换算币种余额';
                } else {
                    conversionModule.style.display = 'none';
                    toggleBtn.textContent = '显示换算币种余额';
                }
            });
            
            // 币种设置模态框
            const settingsBtn = document.getElementById('currencySettings');
            const settingsModal = document.getElementById('currencySettingsModal');
            const closeModal = document.querySelector('.close-modal');
            const saveSettings = document.getElementById('saveSettings');
            
            settingsBtn.addEventListener('click', function() {
                settingsModal.style.display = 'block';
            });
            
            closeModal.addEventListener('click', function() {
                settingsModal.style.display = 'none';
            });
            
            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                if (event.target === settingsModal) {
                    settingsModal.style.display = 'none';
                }
            });
            
            // 保存设置
            saveSettings.addEventListener('click', function() {
                // 获取选中的币种
                const selectedCurrencies = [];
                document.querySelectorAll('.currency-toggles input:checked').forEach(function(checkbox) {
                    selectedCurrencies.push(checkbox.value);
                });
                
                // 默认使用CNY作为换算货币
                const conversionCurrency = 'CNY';
                
                // 初始化设置
                updateCurrencyList(['PHP', 'NGN', 'IDR']);
                updateConversionCurrency('CNY');
                
                // 关闭模态框
                settingsModal.style.display = 'none';
            });
            
            // 显示提示消息
            function showToast(message) {
                const toast = document.createElement('div');
                toast.style.position = 'fixed';
                toast.style.bottom = '20px';
                toast.style.left = '50%';
                toast.style.transform = 'translateX(-50%)';
                toast.style.background = 'rgba(0, 102, 255, 0.8)';
                toast.style.color = 'white';
                toast.style.padding = '10px 20px';
                toast.style.borderRadius = '4px';
                toast.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                toast.style.zIndex = '1000';
                toast.style.transition = 'opacity 0.3s ease';
                toast.textContent = message;
                
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.style.opacity = '0';
                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 300);
                }, 2000);
            }
            
            // 更新币种列表
            function updateCurrencyList(currencies) {
                console.log('更新币种列表:', currencies);
                // 检查本地存储中是否有保存的顺序
                const savedOrder = localStorage.getItem('currencyOrder');
                if (savedOrder) {
                    try {
                        console.log('找到保存的币种顺序:', savedOrder);
                        const orderArray = JSON.parse(savedOrder);
                        // 确保所有币种都在列表中
                        const missingCurrencies = currencies.filter(c => !orderArray.includes(c));
                        if (missingCurrencies.length > 0) {
                            console.log('添加缺失的币种:', missingCurrencies);
                            orderArray.push(...missingCurrencies);
                        }
                        // 去除不存在的币种
                        currencies = orderArray.filter(c => currencies.includes(c));
                        console.log('最终币种顺序:', currencies);
                    } catch (e) {
                        console.error('Error parsing saved currency order:', e);
                    }
                } else {
                    console.log('没有找到保存的币种顺序');
                }
                
                const currencyList = document.getElementById('sortable-currencies');
                currencyList.innerHTML = '';
                
                // 添加选中的币种
                currencies.forEach((currency, index) => {
                    // 获取币种符号和国旗代码
                    const symbol = getCurrencySymbol(currency);
                    const flagCode = getCurrencyFlagCode(currency);
                    
                    const item = document.createElement('div');
                    
                    // 如果是NOK，替换为操作按钮区域
                    if (currency === 'NOK') {
                        item.className = 'currency-item';
                        item.style.display = 'flex';
                        item.style.flexDirection = 'column';
                        item.style.justifyContent = 'center';
                        item.style.background = '#f9f9f9';
                        item.style.cursor = 'default';
                        
                        item.innerHTML = `
                            <div style="display: flex; flex-direction: column; gap: 10px; padding: 8px 0;">
                                <button class="action-btn" style="padding: 8px; font-size: 12px; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); border: 1px solid rgba(0,102,255,0.2); color: #0066cc; box-shadow: 0 2px 5px rgba(0,102,255,0.1); border-radius: 8px; transition: all 0.2s ease;">
                                    <i class="fas fa-paper-plane" style="color: #0066cc; margin-right: 5px;"></i> 付款
                                </button>
                                <button class="action-btn" style="padding: 8px; font-size: 12px; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); border: 1px solid rgba(0,102,255,0.2); color: #0066cc; box-shadow: 0 2px 5px rgba(0,102,255,0.1); border-radius: 8px; transition: all 0.2s ease;">
                                    <i class="fas fa-wallet" style="color: #0066cc; margin-right: 5px;"></i> 提现
                                </button>
                                <button class="action-btn" style="padding: 8px; font-size: 12px; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); border: 1px solid rgba(0,102,255,0.2); color: #0066cc; box-shadow: 0 2px 5px rgba(0,102,255,0.1); border-radius: 8px; transition: all 0.2s ease;">
                                    <i class="fas fa-exchange-alt" style="color: #0066cc; margin-right: 5px;"></i> 换汇
                                </button>
                            </div>
                        `;
                    } else {
                        item.className = 'currency-item';
                        item.setAttribute('data-currency', currency);
                        item.innerHTML = `
                            <div class="currency-header">
                                <div class="currency-flag">
                                    <img src="https://flagcdn.com/w80/${flagCode}.png" alt="${currency} flag">
                                    <span class="currency-code-icon">${symbol}</span>
                                </div>
                                <div class="currency-code">${currency}</div>
                            </div>
                            <div class="currency-amount">0.00</div>
                            <div class="drag-handle">
                                <i class="fas fa-grip-lines"></i>
                            </div>
                        `;
                    }
                    
                    currencyList.appendChild(item);
                });
            }
            
            // 获取币种符号
            function getCurrencySymbol(currency) {
                const currencySymbols = {
                    'PHP': '₱',
                    'NGN': '₦',
                    'IDR': 'Rp',
                    'NOK': 'kr',
                    'KRW': '₩',
                    'VND': '₫',
                    'INR': '₹',
                    'CNY': '¥',
                    'CNH': '¥'
                };
                
                return currencySymbols[currency] || currency.substring(0, 1);
            }
            
            // 获取国旗代码
            function getCurrencyFlagCode(currency) {
                const flagCodes = {
                    'PHP': 'ph',  // 菲律宾
                    'NGN': 'ng',  // 尼日利亚
                    'IDR': 'id',  // 印度尼西亚
                    'NOK': 'no',  // 挪威
                    'KRW': 'kr',  // 韩国
                    'VND': 'vn',  // 越南
                    'INR': 'in',  // 印度
                    'CNY': 'cn',  // 中国
                    'CNH': 'cn'   // 中国(离岸)
                };
                
                return flagCodes[currency] || 'un'; // 默认使用联合国旗布
            }
            
            // 更新换算货币下拉框
            function updateConversionCurrency(currency) {
                const conversionSelect = document.getElementById('conversionCurrency');
                conversionSelect.value = currency;
            }
        });
    </script>
</body>
</html>
