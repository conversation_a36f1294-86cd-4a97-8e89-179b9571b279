<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EurewaX Cross-Border Cloud Payment Platform - Business Architecture</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        @media print {
            body {
                background-color: white !important;
                background-image: none !important;
                padding: 0 !important;
                margin: 0 !important;
            }
            
            .container {
                box-shadow: none !important;
                border: none !important;
                background: white !important;
                padding: 20px !important;
            }
            
            .title h1 {
                color: #2565f7 !important;
                -webkit-text-fill-color: #2565f7 !important;
            }
            
            .title p, .layer-title, .entity-title, .entity-desc, .service-title, .service-desc, .footer-note {
                color: #333 !important;
            }
            
            .print-button {
                display: none !important;
            }
            
            .layer-container {
                background: rgba(245, 245, 245, 0.5) !important;
                border: 1px solid #eee !important;
            }
            
            .entity, .service {
                background: white !important;
                border: 1px solid #ddd !important;
            }
            
            .connector {
                background: #2565f7 !important;
            }
            
            .connector::before, .connector::after {
                background: #2565f7 !important;
                box-shadow: none !important;
            }
            
            .entity-icon {
                print-color-adjust: exact !important;
                -webkit-print-color-adjust: exact !important;
            }
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .print-button:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }
        
        :root {
            --primary-color: #2565f7;
            --primary-gradient: linear-gradient(135deg, #2565f7, #5d38eb);
            --secondary-color: #0b31a0;
            --dark-color: #051552;
            --accent-color: #00e5ff;
            --light-color: #e6f7ff;
            --text-color: #333333;
            --light-text: #ffffff;
            --bg-color: #040d21;
            --card-bg: rgba(255, 255, 255, 0.95);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --box-shadow: 0 8px 32px 0 rgba(0, 0, 20, 0.1);
            
            /* Additional colors */
            --web2-color: #4caf50;
            --web3-color: #9c27b0;
            --bank-color: #ff9800;
            --enterprise-color: #2196f3;
            --risk-color: #f44336;
            --ucard-color: #00bcd4;
            --crypto-color: #673ab7;
            --payment-color: #3f51b5;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }
        
        body {
            color: var(--text-color);
            background-color: var(--bg-color);
            line-height: 1.6;
            overflow-x: hidden;
            background-image: radial-gradient(circle at 50% 50%, rgba(37, 101, 247, 0.1) 0%, rgba(5, 21, 82, 0.1) 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px;
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .title {
            text-align: center;
            margin-bottom: 40px;
            color: var(--light-text);
        }
        
        .title h1 {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(to right, var(--light-text), var(--accent-color));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            display: inline-block;
        }
        
        .title p {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .architecture-diagram {
            width: 100%;
            position: relative;
        }
        
        /* Architecture diagram styles */
        .layer {
            margin-bottom: 30px;
            position: relative;
        }
        
        .layer-title {
            text-align: center;
            padding: 10px;
            color: var(--light-text);
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
            position: relative;
            z-index: 2;
        }
        
        .layer-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: var(--accent-color);
            border-radius: 3px;
        }
        
        .layer-container {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.08);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .layer-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(37, 101, 247, 0.05), rgba(93, 56, 235, 0.05));
            z-index: -1;
        }
        
        .upper-layer .layer-container {
            background: rgba(33, 150, 243, 0.05);
            border-color: rgba(33, 150, 243, 0.1);
        }
        
        .middle-layer .layer-container {
            background: rgba(37, 101, 247, 0.08);
            border-color: rgba(37, 101, 247, 0.15);
        }
        
        .lower-layer .layer-container {
            background: rgba(76, 175, 80, 0.05);
            border-color: rgba(76, 175, 80, 0.1);
        }
        
        .entities {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
        }
        
        .entity {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            padding: 15px;
            min-width: 200px;
            flex: 1;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100px;
        }
        
        .entity:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }
        
        .entity-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--light-text);
            margin-bottom: 8px;
        }
        
        .entity-desc {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .entity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 18px;
            color: var(--light-text);
        }
        
        /* Entity colors */
        .enterprise .entity-icon {
            background: var(--enterprise-color);
        }
        
        .web2 .entity-icon {
            background: var(--web2-color);
        }
        
        .web3 .entity-icon {
            background: var(--web3-color);
        }
        
        .bank .entity-icon {
            background: var(--bank-color);
        }
        
        .risk .entity-icon {
            background: var(--risk-color);
        }
        
        .payment .entity-icon {
            background: var(--payment-color);
        }
        
        .crypto .entity-icon {
            background: var(--crypto-color);
        }
        
        .ucard .entity-icon {
            background: var(--ucard-color);
        }
        
        /* Connector lines */
        .connector {
            position: absolute;
            left: 50%;
            bottom: -30px;
            transform: translateX(-50%);
            width: 2px;
            height: 30px;
            background: var(--accent-color);
            z-index: 1;
        }
        
        .connector::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: var(--accent-color);
            box-shadow: 0 0 10px var(--accent-color);
        }
        
        .connector::after {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: var(--accent-color);
            box-shadow: 0 0 10px var(--accent-color);
        }
        
        /* Middle layer special styles */
        .middle-services {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 20px;
        }
        
        .service {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            padding: 12px;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .service:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            background: rgba(255, 255, 255, 0.12);
        }
        
        .service-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--light-text);
            margin-bottom: 5px;
        }
        
        .service-desc {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        /* Responsive design */
        @media (max-width: 992px) {
            .middle-services {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .entities {
                flex-direction: column;
            }
            
            .entity {
                width: 100%;
            }
            
            .middle-services {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 20px;
            }
            
            .title h1 {
                font-size: 28px;
            }
            
            .title p {
                font-size: 16px;
            }
        }
        
        /* Animation effects */
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(0, 229, 255, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(0, 229, 255, 0); }
            100% { box-shadow: 0 0 0 0 rgba(0, 229, 255, 0); }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        /* Platform logo */
        .platform-logo {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary-gradient);
            color: var(--light-text);
            padding: 8px 25px;
            border-radius: 30px;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 5px 15px rgba(37, 101, 247, 0.3);
            z-index: 10;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .platform-logo span {
            color: var(--accent-color);
        }
        
        /* Footer note */
        .footer-note {
            margin-top: 40px;
            text-align: center;
            color: rgba(255, 255, 255, 0.5);
            font-size: 14px;
            max-width: 800px;
        }
    </style>
</head>
<body>
    <button class="print-button" onclick="window.print()">Export PDF</button>
    <div class="container">
        <div class="title">
            <h1>EurewaX Cross-Border Cloud Payment Platform - Business Architecture</h1>
            <p>Revolutionizing Global Payments · Connecting Borderless Opportunities</p>
        </div>
        
        <div class="architecture-diagram">
            <!-- Upper Layer -->
            <div class="layer upper-layer">
                <div class="layer-title">Upper Layer - Clients & Partners</div>
                <div class="layer-container">
                    <div class="entities">
                        <div class="entity enterprise">
                            <div class="entity-icon"><i class="fas">🏢</i></div>
                            <div class="entity-title">Cross-Border Enterprises</div>
                            <div class="entity-desc">Global Trade & Service Companies</div>
                        </div>
                        
                        <div class="entity web2">
                            <div class="entity-icon"><i class="fas">💻</i></div>
                            <div class="entity-title">Traditional Payment Providers</div>
                            <div class="entity-desc">Global PSPs & Payment Institutions</div>
                        </div>
                        
                        <div class="entity web3">
                            <div class="entity-icon"><i class="fas">⛓️</i></div>
                            <div class="entity-title">Digital Asset Payment Providers</div>
                            <div class="entity-desc">Blockchain Payment & Settlement Services</div>
                        </div>
                        
                        <div class="entity bank">
                            <div class="entity-icon"><i class="fas">🏦</i></div>
                            <div class="entity-title">Banks</div>
                            <div class="entity-desc">Traditional Financial Institutions</div>
                        </div>
                    </div>
                </div>
                <div class="connector"></div>
            </div>
            
            <!-- Middle Layer -->
            <div class="layer middle-layer">
                <div class="platform-logo">EurewaX<span>Cloud</span>Pay</div>
                <div class="layer-title">Middle Layer - EurewaX Cross-Border Cloud Payment Platform</div>
                <div style="text-align: center; margin-bottom: 15px; color: rgba(255, 255, 255, 0.7); font-size: 14px;">Global Payment & Fund Management One-Stop Solution</div>
                <div class="layer-container">
                    <div class="entities">
                        <div class="entity payment">
                            <div class="entity-icon"><i class="fas">💱</i></div>
                            <div class="entity-title">Multi-Currency Payments</div>
                            <div class="entity-desc">Fiat & Digital Asset Payment Network</div>
                        </div>
                        
                        <div class="entity ucard">
                            <div class="entity-icon"><i class="fas">💳</i></div>
                            <div class="entity-title">Virtual Card Services</div>
                            <div class="entity-desc">U-Card & Global Payment Card Issuance</div>
                        </div>
                        
                        <div class="entity web3">
                            <div class="entity-icon"><i class="fas">🌐</i></div>
                            <div class="entity-title">Global Acquiring</div>
                            <div class="entity-desc">Traditional & Digital Asset Payment Solutions</div>
                        </div>
                    </div>
                    
                    <div class="middle-services">
                        <div class="service">
                            <div class="service-title">Unified Account Management</div>
                            <div class="service-desc">Multi-Currency & Asset Centralized Management</div>
                        </div>
                        
                        <div class="service">
                            <div class="service-title">Intelligent Clearing & Settlement</div>
                            <div class="service-desc">Cross-Border Funds & Digital Asset Clearing</div>
                        </div>
                        
                        <div class="service">
                            <div class="service-title">Real-time FX Management</div>
                            <div class="service-desc">Multi-Currency Rate Optimization & Risk Management</div>
                        </div>
                        
                        <div class="service">
                            <div class="service-title">Global Compliance Framework</div>
                            <div class="service-desc">Cross-Border Payment & Asset Compliance Solutions</div>
                        </div>
                        
                        <div class="service">
                            <div class="service-title">Transaction Monitoring & Analytics</div>
                            <div class="service-desc">Omni-Channel Transaction Monitoring & Data Analysis</div>
                        </div>
                        
                        <div class="service">
                            <div class="service-title">Open Platform Ecosystem</div>
                            <div class="service-desc">Open APIs & Application Ecosystem</div>
                        </div>
                    </div>
                </div>
                <div class="connector"></div>
            </div>
            
            <!-- Lower Layer -->
            <div class="layer lower-layer">
                <div class="layer-title">Lower Layer - Financial & Service Network</div>
                <div class="layer-container">
                    <div class="entities">
                        <div class="entity bank">
                            <div class="entity-icon"><i class="fas">🏦</i></div>
                            <div class="entity-title">Banks</div>
                            <div class="entity-desc">Global Banking Network</div>
                        </div>
                        
                        <div class="entity web2">
                            <div class="entity-icon"><i class="fas">💳</i></div>
                            <div class="entity-title">Global Payment Service Providers</div>
                            <div class="entity-desc">International PSPs & Acquiring Institutions</div>
                        </div>
                        
                        <div class="entity web3">
                            <div class="entity-icon"><i class="fas">⛓️</i></div>
                            <div class="entity-title">Digital Asset Service Providers</div>
                            <div class="entity-desc">Digital Asset Exchange & Custody Institutions</div>
                        </div>
                        
                        <div class="entity risk">
                            <div class="entity-icon"><i class="fas">🔒</i></div>
                            <div class="entity-title">Risk & Compliance Service Providers</div>
                            <div class="entity-desc">Global Compliance & Risk Management Services</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer-note">
            EurewaX Cross-Border Cloud Payment Platform connects upper-layer clients with lower-layer financial networks through innovative technology architecture,
            providing secure, efficient, and flexible global fund flow and management solutions.
            The platform integrates multi-currency payments, virtual card services, and global acquiring capabilities, supporting omni-channel processing
            of fiat and digital assets to meet the diverse needs of cross-border enterprises, traditional payment providers, digital asset payment providers, and banks.
        </div>
    </div>
    
    <script>
        // Add icon library
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css';
        document.head.appendChild(link);
        
        // Auto-adjust page size for printing
        window.onbeforeprint = function() {
            document.body.classList.add('printing');
        };
        
        window.onafterprint = function() {
            document.body.classList.remove('printing');
        };
    </script>
</body>
</html>
