<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>非洲贸易与支付格局</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        .slide {
            width: 100%;
            max-width: 1200px;
            min-height: 100vh;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            position: relative;
            display: flex;
            flex-direction: column;
        }
        .header {
            background-color: #003366;
            color: white;
            padding: 15px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
        }
        .header p {
            margin: 5px 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            display: flex;
            flex-wrap: wrap;
            flex: 1;
            padding: 10px;
        }
        .map-container {
            flex: 1 1 600px;
            min-width: 300px;
            padding: 10px;
            display: flex;
            flex-direction: column;
        }
        .info-container {
            flex: 1 1 500px;
            min-width: 300px;
            padding: 10px;
            display: flex;
            flex-direction: column;
        }
        #africaMap {
            width: 100%;
            height: 450px;
            margin-bottom: 15px;
        }
        .africa-map-container {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .region {
            transition: fill 0.3s ease;
            cursor: pointer;
        }
        .region:hover {
            fill-opacity: 0.8;
            stroke-width: 2;
        }
        .region-info {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .region-card {
            background-color: #f9f9f9;
            border-left: 4px solid #003366;
            padding: 8px;
            flex: 1 1 45%;
            min-width: 200px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .region-card h3 {
            margin-top: 0;
            color: #003366;
            font-size: 16px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .region-card p {
            margin: 4px 0;
            font-size: 13px;
            line-height: 1.3;
        }
        .payment-chart {
            width: 100%;
            height: 280px;
            margin-top: 10px;
        }
        .footer {
            background-color: #f0f0f0;
            padding: 8px;
            text-align: center;
            font-size: 13px;
            color: #666;
        }
        @media (max-width: 768px) {
            .content {
                flex-direction: column;
            }
            #africaMap {
                height: 350px;
            }
            .payment-chart {
                height: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="header">
            <h1>非洲贸易与支付解决方案</h1>
            <p>Africa Trade & Payment Solutions for China-Africa Cooperation</p>
            <div style="margin-top: 5px; font-size: 14px; opacity: 0.8;">中非贸易峰会 · 2024</div>
        </div>

        <div class="content">
            <div class="map-container">
                <div class="africa-map-container">
                    <svg id="africaMap" viewBox="0 0 800 900" xmlns="http://www.w3.org/2000/svg">
                        <!-- 非洲大陆轮廓 -->
                        <path d="M400,150 C450,170 500,200 520,250 C540,300 550,350 560,400 C570,450 580,500 570,550 C560,600 540,650 500,680 C460,710 420,730 380,740 C340,750 300,740 270,720 C240,700 220,670 210,630 C200,590 200,550 210,510 C220,470 240,430 230,390 C220,350 200,320 210,280 C220,240 250,210 280,190 C310,170 350,160 400,150 Z"
                              fill="#e0f3f8" stroke="#333" stroke-width="2"/>

                        <!-- 北非区域 -->
                        <path d="M400,150 C450,170 500,200 520,250 C540,300 550,350 450,370 C350,390 300,380 250,350 C200,320 200,280 210,280 C220,240 250,210 280,190 C310,170 350,160 400,150 Z"
                              fill="#abd9e9" stroke="#333" stroke-width="1" class="region" id="northAfrica"/>
                        <text x="380" y="250" font-size="24" font-weight="bold" text-anchor="middle">北非</text>
                        <text x="380" y="280" font-size="16" text-anchor="middle">埃及、摩洛哥、阿尔及利亚</text>

                        <!-- 西非区域 -->
                        <path d="M250,350 C300,380 350,390 370,450 C390,510 350,550 320,560 C290,570 260,560 240,540 C220,520 210,490 210,460 C210,430 220,400 230,390 C220,350 200,320 210,280 C220,240 200,320 250,350 Z"
                              fill="#74add1" stroke="#333" stroke-width="1" class="region" id="westAfrica"/>
                        <text x="290" y="450" font-size="24" font-weight="bold" text-anchor="middle">西非</text>
                        <text x="290" y="480" font-size="16" text-anchor="middle">尼日利亚、加纳</text>

                        <!-- 东非区域 -->
                        <path d="M450,370 C550,350 560,400 570,450 C580,500 580,550 550,580 C520,610 480,620 450,600 C420,580 400,550 390,510 C380,470 350,450 370,450 C390,510 450,370 450,370 Z"
                              fill="#4575b4" stroke="#333" stroke-width="1" class="region" id="eastAfrica"/>
                        <text x="480" y="500" font-size="24" font-weight="bold" text-anchor="middle">东非</text>
                        <text x="480" y="530" font-size="16" text-anchor="middle">肯尼亚、埃塞俄比亚</text>

                        <!-- 南部非洲区域 -->
                        <path d="M320,560 C350,550 390,510 420,580 C450,600 480,620 450,650 C420,680 380,700 350,710 C320,720 290,710 270,690 C250,670 240,640 240,610 C240,580 260,560 290,570 C320,560 320,560 320,560 Z"
                              fill="#313695" stroke="#333" stroke-width="1" class="region" id="southernAfrica"/>
                        <text x="370" y="650" font-size="24" font-weight="bold" text-anchor="middle" fill="white">南部非洲</text>
                        <text x="370" y="680" font-size="16" text-anchor="middle" fill="white">南非、安哥拉</text>
                    </svg>
                </div>

                <div class="region-info">
                    <div class="region-card" style="border-left-color: #2ecc71;">
                        <h3>东非 (East Africa)</h3>
                        <p style="font-size: 12px;"><b>国家</b>：肯尼亚、埃塞俄比亚、坦桑尼亚</p>
                        <p style="font-size: 12px;"><b>贸易</b>：交通基建、制造业园区、数字经济</p>
                        <p style="font-size: 12px;"><b>公司</b>：中国路桥、华为、阿里巴巴</p>
                        <p style="font-size: 12px;"><b>支付</b>：钱包(75%)、现金(15%)、银行(8%)、国际卡(2%)</p>
                    </div>

                    <div class="region-card" style="border-left-color: #3498db;">
                        <h3>西非 (West Africa)</h3>
                        <p style="font-size: 12px;"><b>国家</b>：尼日利亚、加纳、科特迪瓦</p>
                        <p style="font-size: 12px;"><b>贸易</b>：石油天然气、农业、电信</p>
                        <p style="font-size: 12px;"><b>公司</b>：中石化、中兴、中粮</p>
                        <p style="font-size: 12px;"><b>支付</b>：现金(40%)、银行(35%)、钱包(20%)、国际卡(5%)</p>
                    </div>

                    <div class="region-card" style="border-left-color: #f1c40f;">
                        <h3>南部非洲 (Southern Africa)</h3>
                        <p style="font-size: 12px;"><b>国家</b>：南非、安哥拉、赞比亚</p>
                        <p style="font-size: 12px;"><b>贸易</b>：矿产资源、制造业、金融服务</p>
                        <p style="font-size: 12px;"><b>公司</b>：中国五矿、工商银行、比亚迪</p>
                        <p style="font-size: 12px;"><b>支付</b>：国际卡(40%)、钱包(25%)、现金(20%)、银行(15%)</p>
                    </div>

                    <div class="region-card" style="border-left-color: #e74c3c;">
                        <h3>北非 (North Africa)</h3>
                        <p style="font-size: 12px;"><b>国家</b>：埃及、摩洛哥、阿尔及利亚</p>
                        <p style="font-size: 12px;"><b>贸易</b>：基建、能源、制造业</p>
                        <p style="font-size: 12px;"><b>公司</b>：中建、中石油、华为</p>
                        <p style="font-size: 12px;"><b>支付</b>：现金(58%)、国际卡(20%)、银行(15%)、钱包(7%)</p>
                    </div>
                </div>
            </div>

            <div class="info-container">
                <div class="payment-chart" id="paymentChart"></div>

                <div style="margin-top: 10px; background: linear-gradient(to right, #f9f9f9, #f0f0f0); border-radius: 8px; padding: 15px; box-shadow: 0 3px 6px rgba(0,0,0,0.1);">
                    <h3 style="color: #003366; text-align: center; font-size: 18px; margin-top: 0; margin-bottom: 15px; border-bottom: 2px solid #003366; padding-bottom: 8px;">
                        Ipaylinks 非洲全覆盖支付解决方案
                        <span style="font-size: 14px; display: block; color: #666; font-weight: normal; margin-top: 5px;">中非贸易合作的金融桥梁</span>
                    </h3>

                    <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                        <div style="flex: 1 1 48%; min-width: 200px;">
                            <h4 style="color: #2c3e50; font-size: 15px; margin-top: 0; margin-bottom: 8px;">
                                <span style="background: #003366; color: white; padding: 2px 8px; border-radius: 3px; margin-right: 5px;">01</span>
                                全方位本地支付接入
                            </h4>
                            <ul style="font-size: 13px; line-height: 1.4; margin: 5px 0; padding-left: 20px; color: #444;">
                                <li><b>移动钱包全覆盖</b>：已接入M-Pesa(肯尼亚)、MTN/Vodafone(加纳)、SnapScan(南非)等主流钱包</li>
                                <li><b>银行体系深度整合</b>：对接尼日利亚、南非、埃及等地区主要银行，支持实时转账</li>
                                <li><b>国际卡收单优势</b>：在南非、埃及等旅游热点地区提供低费率国际卡收单</li>
                                <li><b>现金解决方案</b>：针对摩洛哥等现金使用率高地区，提供线下代收与电子化转换服务</li>
                            </ul>
                        </div>

                        <div style="flex: 1 1 48%; min-width: 200px;">
                            <h4 style="color: #2c3e50; font-size: 15px; margin-top: 0; margin-bottom: 8px;">
                                <span style="background: #003366; color: white; padding: 2px 8px; border-radius: 3px; margin-right: 5px;">02</span>
                                中非贸易场景定制方案
                            </h4>
                            <div style="display: flex; flex-wrap: wrap; gap: 8px; font-size: 13px;">
                                <div style="flex: 1 1 48%; min-width: 90px; background: rgba(0,51,102,0.05); padding: 8px; border-radius: 5px; border-left: 3px solid #003366;">
                                    <p style="margin: 0; font-weight: bold; color: #003366;">基建工程款结算</p>
                                    <p style="margin: 4px 0 0; color: #444;">支持大额分批结算、多币种账户、资金安全监管</p>
                                </div>
                                <div style="flex: 1 1 48%; min-width: 90px; background: rgba(0,51,102,0.05); padding: 8px; border-radius: 5px; border-left: 3px solid #003366;">
                                    <p style="margin: 0; font-weight: bold; color: #003366;">资源贸易收付款</p>
                                    <p style="margin: 4px 0 0; color: #444;">石油、矿产交易的安全托管、分账与结汇服务</p>
                                </div>
                                <div style="flex: 1 1 48%; min-width: 90px; background: rgba(0,51,102,0.05); padding: 8px; border-radius: 5px; border-left: 3px solid #003366;">
                                    <p style="margin: 0; font-weight: bold; color: #003366;">制造业供应链</p>
                                    <p style="margin: 4px 0 0; color: #444;">批量付款、员工薪资发放、供应商结算一体化</p>
                                </div>
                                <div style="flex: 1 1 48%; min-width: 90px; background: rgba(0,51,102,0.05); padding: 8px; border-radius: 5px; border-left: 3px solid #003366;">
                                    <p style="margin: 0; font-weight: bold; color: #003366;">跨境电商收款</p>
                                    <p style="margin: 4px 0 0; color: #444;">适配各地区主流支付习惯，提升交易转化率</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 15px; background: rgba(0,51,102,0.03); padding: 10px; border-radius: 5px; text-align: center;">
                        <p style="margin: 0; font-size: 14px; color: #003366; font-weight: bold;">
                            Ipaylinks已为200+中国企业提供非洲支付服务，交易覆盖40+非洲国家，年交易额超50亿美元
                        </p>
                        <p style="margin: 5px 0 0; font-size: 12px; color: #666;">
                            合规安全 · 本地化服务 · 7×24小时技术支持 · 专业风控团队
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>© 2024 Ipaylinks - 中非贸易支付解决方案提供商</div>
                <div style="font-weight: bold; color: #003366;">联系方式：<EMAIL> | +86 10 8888 8888</div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script>
        // 为SVG地图添加交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const regions = document.querySelectorAll('.region');
            const regionCards = document.querySelectorAll('.region-card');

            // 为每个区域添加鼠标悬停效果
            regions.forEach(region => {
                region.addEventListener('mouseover', function() {
                    const regionId = this.id;
                    highlightRegionCard(regionId);
                });

                region.addEventListener('mouseout', function() {
                    resetRegionCards();
                });

                region.addEventListener('click', function() {
                    const regionId = this.id;
                    scrollToRegionCard(regionId);
                });
            });

            // 高亮对应的区域卡片
            function highlightRegionCard(regionId) {
                resetRegionCards();

                let cardIndex = -1;
                switch(regionId) {
                    case 'eastAfrica':
                        cardIndex = 0;
                        break;
                    case 'westAfrica':
                        cardIndex = 1;
                        break;
                    case 'southernAfrica':
                        cardIndex = 2;
                        break;
                    case 'northAfrica':
                        cardIndex = 3;
                        break;
                }

                if(cardIndex >= 0 && cardIndex < regionCards.length) {
                    regionCards[cardIndex].style.boxShadow = '0 0 10px rgba(0,0,0,0.3)';
                    regionCards[cardIndex].style.transform = 'scale(1.03)';
                    regionCards[cardIndex].style.transition = 'all 0.3s ease';
                }
            }

            // 重置所有区域卡片样式
            function resetRegionCards() {
                regionCards.forEach(card => {
                    card.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                    card.style.transform = 'scale(1)';
                });
            }

            // 滚动到对应的区域卡片
            function scrollToRegionCard(regionId) {
                let cardIndex = -1;
                switch(regionId) {
                    case 'eastAfrica':
                        cardIndex = 0;
                        break;
                    case 'westAfrica':
                        cardIndex = 1;
                        break;
                    case 'southernAfrica':
                        cardIndex = 2;
                        break;
                    case 'northAfrica':
                        cardIndex = 3;
                        break;
                }

                if(cardIndex >= 0 && cardIndex < regionCards.length) {
                    regionCards[cardIndex].scrollIntoView({behavior: 'smooth', block: 'center'});
                }
            }
        });

        // 支付方式渗透率图表
        const paymentChart = echarts.init(document.getElementById('paymentChart'));
        const paymentOption = {
            title: {
                text: '非洲各地区支付方式渗透率',
                left: 'center',
                textStyle: {
                    fontSize: 14
                },
                top: 5
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['现金支付', '国际卡支付', '银行转账', '移动钱包'],
                top: 25,
                textStyle: {
                    fontSize: 12
                },
                itemWidth: 12,
                itemHeight: 8
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '60px',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['东非', '西非', '南非', '北非'],
                axisLabel: {
                    fontSize: 12
                }
            },
            yAxis: {
                type: 'value',
                name: '渗透率(%)',
                max: 100,
                nameTextStyle: {
                    fontSize: 12
                },
                axisLabel: {
                    fontSize: 11
                }
            },
            series: [
                {
                    name: '现金支付',
                    type: 'bar',
                    stack: 'total',
                    emphasis: {
                        focus: 'series'
                    },
                    data: [15, 40, 20, 58]
                },
                {
                    name: '国际卡支付',
                    type: 'bar',
                    stack: 'total',
                    emphasis: {
                        focus: 'series'
                    },
                    data: [2, 5, 40, 20]
                },
                {
                    name: '银行转账',
                    type: 'bar',
                    stack: 'total',
                    emphasis: {
                        focus: 'series'
                    },
                    data: [8, 35, 15, 15]
                },
                {
                    name: '移动钱包',
                    type: 'bar',
                    stack: 'total',
                    emphasis: {
                        focus: 'series'
                    },
                    data: [75, 20, 25, 7]
                }
            ]
        };

        paymentChart.setOption(paymentOption);

        // 响应式调整
        window.addEventListener('resize', function() {
            africaMap.resize();
            paymentChart.resize();
        });
    </script>
</body>
</html>
