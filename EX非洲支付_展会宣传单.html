<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EX非洲支付解决方案 - 宣传单</title>
    <style>
        @media print {
            body { margin: 0; padding: 0; background: white !important; }
            .no-print { display: none !important; }
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            min-height: 100vh;
        }
        
        .flyer {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
        }
        
        .header h1 {
            font-size: 36px;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }
        
        .header .subtitle {
            font-size: 18px;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .content {
            padding: 40px;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .highlight-box h2 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 30px 0;
        }
        
        .stat-card {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        
        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .feature:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        
        .feature-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }
        
        .feature-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .feature-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }
        
        .regions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        
        .region {
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .region.east { 
            background: #e8f5e8; 
            border-left-color: #4CAF50; 
        }
        
        .region.west { 
            background: #e3f2fd; 
            border-left-color: #2196F3; 
        }
        
        .region.south { 
            background: #fff3e0; 
            border-left-color: #FF9800; 
        }
        
        .region.north { 
            background: #ffebee; 
            border-left-color: #F44336; 
        }
        
        .region-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        
        .region-info {
            font-size: 12px;
            color: #666;
        }
        
        .contact-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-top: 30px;
        }
        
        .contact-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 15px;
        }
        
        .contact-item {
            text-align: center;
        }
        
        .contact-icon {
            font-size: 24px;
            margin-bottom: 8px;
            color: #667eea;
        }
        
        .contact-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .contact-value {
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }
        
        .qr-section {
            text-align: center;
            margin-top: 20px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        
        .print-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .stats-grid,
            .features,
            .regions,
            .contact-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 28px;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <button class="print-btn no-print" onclick="window.print()">打印宣传单</button>
    
    <div class="flyer">
        <div class="header">
            <h1>🌍 EX非洲支付解决方案</h1>
            <div class="subtitle">中非经贸博览会 · 连接中非贸易，赋能全球支付</div>
        </div>
        
        <div class="content">
            <div class="highlight-box">
                <h2>🚀 一站式非洲支付专家</h2>
                <p>覆盖40+非洲国家，支持150+本地支付方式，为中非贸易提供安全、高效的支付解决方案</p>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">200+</div>
                    <div class="stat-label">中国企业客户</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">40+</div>
                    <div class="stat-label">非洲国家覆盖</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">50亿</div>
                    <div class="stat-label">美元年交易额</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">98.5%</div>
                    <div class="stat-label">支付成功率</div>
                </div>
            </div>
            
            <h3 style="color: #333; margin-bottom: 15px;">🌍 非洲各地区支付覆盖</h3>
            <div class="regions">
                <div class="region east">
                    <div class="region-title">🌅 东非：移动支付领先</div>
                    <div class="region-info">M-Pesa、Airtel Money全覆盖<br>移动钱包渗透率75%</div>
                </div>
                <div class="region west">
                    <div class="region-title">🌅 西非：银行体系发达</div>
                    <div class="region-info">MTN、Orange、Vodafone钱包<br>银行转账占比35%</div>
                </div>
                <div class="region south">
                    <div class="region-title">🌅 南非：国际化程度高</div>
                    <div class="region-info">SnapScan、Zapper、PayFast<br>国际卡支付40%</div>
                </div>
                <div class="region north">
                    <div class="region-title">🌅 北非：数字化潜力大</div>
                    <div class="region-info">Fawry Pay、银行卡支付<br>现金占比58%</div>
                </div>
            </div>
            
            <h3 style="color: #333; margin-bottom: 15px;">💡 核心解决方案</h3>
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">🏗️</div>
                    <div class="feature-title">基建工程款结算</div>
                    <div class="feature-desc">大额分批结算、多币种管理、资金安全监管、进度款自动释放</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">⛽</div>
                    <div class="feature-title">资源贸易收付款</div>
                    <div class="feature-desc">大宗商品托管、智能分账、汇率风险管理、合规审计追踪</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">🏭</div>
                    <div class="feature-title">制造业供应链</div>
                    <div class="feature-desc">批量付款、员工薪资发放、供应链融资、库存质押管理</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">🛒</div>
                    <div class="feature-title">跨境电商收款</div>
                    <div class="feature-desc">本地化体验、多语言支持、智能路由、AI风控反欺诈</div>
                </div>
            </div>
            
            <div class="contact-section">
                <h3 style="color: #333; text-align: center; margin-bottom: 10px;">📞 联系我们</h3>
                <div class="contact-grid">
                    <div class="contact-item">
                        <div class="contact-icon">📧</div>
                        <div class="contact-label">商务合作</div>
                        <div class="contact-value"><EMAIL></div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">🌐</div>
                        <div class="contact-label">官方网站</div>
                        <div class="contact-value">www.eurewax.com</div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">📱</div>
                        <div class="contact-label">24小时热线</div>
                        <div class="contact-value">+65 6000 0000</div>
                    </div>
                </div>
            </div>
            
            <div class="qr-section">
                <h4>🎪 中非经贸博览会展台</h4>
                <p>现场演示 · API对接指导 · 专家咨询</p>
                <p style="margin-top: 10px; font-size: 14px;">扫码关注或现场咨询，获取详细技术文档</p>
            </div>
        </div>
    </div>
</body>
</html>
